import React, { useState, useRef } from 'react';
import UploadForm from './UploadForm';
import ResultView from './ResultView';

function App() {
  const [result, setResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [originalImage, setOriginalImage] = useState(null);
  const uploadFormRef = useRef(null);

  const handleUpload = async (formData) => {
    setIsLoading(true);
    setError(null);
    
    // 保存原始图像用于对比
    const file = formData.get('file');
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => setOriginalImage(e.target.result);
      reader.readAsDataURL(file);
    }

    try {
      const response = await fetch('http://localhost:8001/enhance/', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '处理失败');
      }

      const data = await response.json();
      setResult(data);
    } catch (err) {
      console.error('上传失败:', err);
      setError(err.message || '网络错误，请检查后端服务是否正常运行');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setResult(null);
    setError(null);
    setOriginalImage(null);
  };

  const handleApply = () => {
    if (uploadFormRef.current && uploadFormRef.current.triggerSubmit) {
      uploadFormRef.current.triggerSubmit();
    }
  };

  return (
    <div style={{ 
      height: '100vh',
      backgroundColor: '#2b2b2b',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* macOS风格标题栏 */}
      <div style={{
        height: '28px',
        backgroundColor: '#3c3c3c',
        display: 'flex',
        alignItems: 'center',
        paddingLeft: '12px',
        borderBottom: '1px solid #555'
      }}>
        <div style={{ display: 'flex', gap: '8px' }}>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>
        </div>
        <div style={{ 
          flex: 1, 
          textAlign: 'center', 
          color: '#fff', 
          fontSize: '13px',
          fontWeight: '500'
        }}>
          图像增强工具
        </div>
      </div>

      {/* 菜单栏 */}
      <div style={{
        height: '32px',
        backgroundColor: '#3c3c3c',
        display: 'flex',
        alignItems: 'center',
        paddingLeft: '16px',
        borderBottom: '1px solid #555'
      }}>
        {['打开', '保存', '编辑', '调整', '效果', '帮助', '实验室功能', '窗口大小', '图像增强', '自动增强'].map((item, index) => (
          <div key={index} style={{
            color: '#ddd',
            fontSize: '13px',
            padding: '6px 12px',
            cursor: 'pointer',
            borderRadius: '4px',
            transition: 'background-color 0.2s'
          }}
          onMouseEnter={(e) => e.target.style.backgroundColor = '#4a4a4a'}
          onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
          >
            {item}
          </div>
        ))}
      </div>

      {/* 主内容区域 */}
      <div style={{ 
        flex: 1, 
        display: 'flex',
        overflow: 'hidden'
      }}>
        {/* 左侧主工作区 */}
        <div style={{
          flex: 1,
          backgroundColor: '#1e1e1e',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative'
        }}>
          {/* 图像显示区域 */}
          <div style={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '20px',
            position: 'relative'
          }}>
            {error && (
              <div style={{ 
                position: 'absolute',
                top: '20px',
                left: '20px',
                right: '20px',
                backgroundColor: '#d32f2f', 
                color: 'white', 
                padding: '12px', 
                borderRadius: '4px',
                fontSize: '14px',
                zIndex: 10
              }}>
                <strong>错误:</strong> {error}
                <button 
                  onClick={handleReset}
                  style={{
                    marginLeft: '10px',
                    padding: '4px 8px',
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '3px',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  重试
                </button>
              </div>
            )}

            {isLoading && (
              <div style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#1e1e1e',
                color: '#ddd'
              }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '48px', marginBottom: '20px' }}>⚡</div>
                  <div style={{ fontSize: '16px', marginBottom: '20px', color: '#ddd' }}>
                    AI正在增强您的图像，请稍候...
                  </div>

                  <div style={{
                    width: '200px',
                    height: '4px',
                    backgroundColor: '#333',
                    borderRadius: '2px',
                    overflow: 'hidden',
                    margin: '0 auto'
                  }}>
                    <div style={{
                      width: '100%',
                      height: '100%',
                      backgroundColor: '#4a90e2',
                      animation: 'loading 1.5s infinite'
                    }}></div>
                  </div>

                  <div style={{
                    fontSize: '12px',
                    color: '#aaa',
                    marginTop: '16px'
                  }}>
                    处理时间取决于图像大小和复杂度
                  </div>
                </div>
              </div>
            )}

            {!result && !isLoading && (
              <div style={{
                textAlign: 'center',
                color: '#888',
                fontSize: '16px'
              }}>
                <div style={{ fontSize: '48px', marginBottom: '20px' }}>📁</div>
                <div>拖拽图像文件到此处或使用右侧面板上传</div>
              </div>
            )}

            {result && <ResultView result={result} originalImage={originalImage} />}
          </div>

          {/* 底部状态栏 */}
          <div style={{
            height: '24px',
            backgroundColor: '#333',
            borderTop: '1px solid #555',
            display: 'flex',
            alignItems: 'center',
            paddingLeft: '12px',
            fontSize: '12px',
            color: '#aaa'
          }}>
            就绪
          </div>
        </div>

        {/* 右侧参数面板 */}
        <div style={{
          width: '280px',
          backgroundColor: '#2b2b2b',
          borderLeft: '1px solid #555',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* 面板标题 */}
          <div style={{
            height: '40px',
            backgroundColor: '#3c3c3c',
            borderBottom: '1px solid #555',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#fff',
            fontSize: '14px',
            fontWeight: '500'
          }}>
            调整面板
          </div>

          {/* 参数控制区域 */}
          <div style={{
            flex: 1,
            overflow: 'auto',
            padding: '0'
          }}>
            <UploadForm
              onUpload={handleUpload}
              isLoading={isLoading}
              ref={uploadFormRef}
            />
          </div>

          {/* 底部按钮区域 */}
          <div style={{
            height: '60px',
            backgroundColor: '#333',
            borderTop: '1px solid #555',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '10px',
            padding: '0 15px'
          }}>
            <button
              onClick={handleReset}
              style={{
                flex: 1,
                padding: '8px',
                backgroundColor: '#555',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '13px'
              }}
            >
              重置
            </button>
            <button
              onClick={handleApply}
              disabled={isLoading}
              style={{
                flex: 1,
                padding: '8px',
                backgroundColor: isLoading ? '#666' : '#4a90e2',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                fontSize: '13px'
              }}
            >
              {isLoading ? '处理中...' : '应用'}
            </button>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes loading {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </div>
  );
}

export default App;
