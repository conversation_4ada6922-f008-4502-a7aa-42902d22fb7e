{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResultView = ({\n  result,\n  originalImage\n}) => {\n  _s();\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const [zoomLevel, setZoomLevel] = useState(1); // 缩放级别\n  const [panOffset, setPanOffset] = useState({\n    x: 0,\n    y: 0\n  }); // 平移偏移\n  const [isPanning, setIsPanning] = useState(false);\n  const [lastPanPoint, setLastPanPoint] = useState({\n    x: 0,\n    y: 0\n  });\n  const containerRef = useRef(null);\n  const enhancedImageRef = useRef(null);\n  const imageContainerRef = useRef(null);\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n    setImageError(false);\n  };\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleMouseDown = e => {\n    setIsDragging(true);\n    e.preventDefault();\n  };\n  const handleMouseMove = e => {\n    if (!isDragging || !containerRef.current) return;\n    const rect = containerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, x / rect.width * 100));\n    setSplitPosition(percentage);\n  };\n  const handleMouseUp = () => {\n    setIsDragging(false);\n    setIsPanning(false);\n  };\n\n  // 缩放处理\n  const handleWheel = e => {\n    if (!imageContainerRef.current) return;\n    e.preventDefault();\n    const delta = e.deltaY > 0 ? -0.1 : 0.1;\n    const newZoom = Math.max(0.5, Math.min(5, zoomLevel + delta));\n    setZoomLevel(newZoom);\n  };\n\n  // 平移处理\n  const handlePanStart = e => {\n    if (zoomLevel <= 1) return; // 只有放大时才允许平移\n\n    setIsPanning(true);\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n    e.preventDefault();\n  };\n  const handlePanMove = e => {\n    if (!isPanning) return;\n    const deltaX = e.clientX - lastPanPoint.x;\n    const deltaY = e.clientY - lastPanPoint.y;\n    setPanOffset(prev => ({\n      x: prev.x + deltaX,\n      y: prev.y + deltaY\n    }));\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n  };\n  const handlePanEnd = () => {\n    setIsPanning(false);\n  };\n\n  // 重置缩放和平移\n  const resetZoom = () => {\n    setZoomLevel(1);\n    setPanOffset({\n      x: 0,\n      y: 0\n    });\n  };\n\n  // 缩放控制函数\n  const zoomIn = () => {\n    setZoomLevel(prev => Math.min(5, prev + 0.25));\n  };\n  const zoomOut = () => {\n    setZoomLevel(prev => Math.max(0.5, prev - 0.25));\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  // 添加平移事件监听\n  useEffect(() => {\n    if (isPanning) {\n      document.addEventListener('mousemove', handlePanMove);\n      document.addEventListener('mouseup', handlePanEnd);\n      return () => {\n        document.removeEventListener('mousemove', handlePanMove);\n        document.removeEventListener('mouseup', handlePanEnd);\n      };\n    }\n  }, [isPanning, lastPanPoint]);\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#ddd'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '40px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        padding: '0 16px',\n        flexShrink: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '13px',\n            color: '#ddd'\n          },\n          children: \"\\u5904\\u7406\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), result.message && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: [\"\\u2022 \", result.message]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '6px',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('split'),\n          style: {\n            padding: '4px 8px',\n            backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u5206\\u5272\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('side-by-side'),\n          style: {\n            padding: '4px 8px',\n            backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u5E76\\u6392\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), viewMode === 'split' && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: resetSplit,\n          style: {\n            padding: '4px 8px',\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u91CD\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), result.params_used && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: showParams ? 'auto' : '32px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        padding: '8px 16px',\n        flexShrink: 0,\n        transition: 'height 0.3s ease'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: \"\\u5904\\u7406\\u53C2\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this), !showParams && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '12px',\n              fontSize: '12px',\n              color: '#ddd'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [result.params_used.scale, \"x\\u8D85\\u5206\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: result.params_used.use_realesrgan ? 'RealESRGAN' : '插值'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u9510\\u5316\", (result.params_used.sharpening * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u7F8E\\u989C\", (result.params_used.beauty * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowParams(!showParams),\n          style: {\n            padding: '2px 6px',\n            backgroundColor: 'transparent',\n            color: '#aaa',\n            border: '1px solid #555',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '11px',\n            transition: 'all 0.2s'\n          },\n          onMouseEnter: e => {\n            e.target.style.backgroundColor = '#555';\n            e.target.style.color = '#fff';\n          },\n          onMouseLeave: e => {\n            e.target.style.backgroundColor = 'transparent';\n            e.target.style.color = '#aaa';\n          },\n          children: showParams ? '▲' : '▼'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this), showParams && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '12px',\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',\n          gap: '8px',\n          fontSize: '12px',\n          color: '#ddd'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u8D85\\u5206\\u500D\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 20\n          }, this), \" \", result.params_used.scale, \"x\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"AI\\u6A21\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 20\n          }, this), \" \", result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u9510\\u5316:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 20\n          }, this), \" \", (result.params_used.sharpening * 100).toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u964D\\u566A:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 20\n          }, this), \" \", result.params_used.denoising]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u9971\\u548C\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 20\n          }, this), \" \", result.params_used.saturation.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u5BF9\\u6BD4\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 20\n          }, this), \" \", result.params_used.contrast.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u4EAE\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 20\n          }, this), \" \", result.params_used.brightness > 0 ? '+' : '', result.params_used.brightness]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u7F8E\\u989C:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 20\n          }, this), \" \", (result.params_used.beauty * 100).toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column',\n        overflow: 'hidden',\n        position: 'relative'\n      },\n      children: viewMode === 'split' ?\n      /*#__PURE__*/\n      // 分割线对比模式\n      _jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          padding: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '12px',\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u5DE6\\u4FA7\\uFF1A\\u539F\\u59CB\\u56FE\\u50CF | \\u53F3\\u4FA7\\uFF1A\\u589E\\u5F3A\\u56FE\\u50CF | \\u5206\\u5272\\u4F4D\\u7F6E\\uFF1A\", splitPosition.toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '4px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: zoomOut,\n              disabled: zoomLevel <= 0.5,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                fontSize: '12px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              title: \"\\u7F29\\u5C0F\",\n              children: \"\\u2212\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                minWidth: '40px',\n                textAlign: 'center',\n                fontSize: '11px',\n                color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n              },\n              children: [(zoomLevel * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: zoomIn,\n              disabled: zoomLevel >= 5,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                color: zoomLevel >= 5 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                fontSize: '12px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              title: \"\\u653E\\u5927\",\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetZoom,\n              disabled: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? '#444' : '#555',\n                color: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? 'not-allowed' : 'pointer',\n                fontSize: '10px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              title: \"\\u91CD\\u7F6E\\u7F29\\u653E\",\n              children: \"\\u2302\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: containerRef,\n          style: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            cursor: isDragging ? 'ew-resize' : 'default',\n            userSelect: 'none',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative',\n              width: '100%',\n              height: '100%',\n              maxHeight: 'calc(100vh - 200px)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: imageContainerRef,\n              style: {\n                position: 'relative',\n                display: 'inline-block',\n                border: '1px solid #555',\n                borderRadius: '4px',\n                overflow: 'hidden',\n                transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                transformOrigin: 'center center',\n                transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                cursor: zoomLevel > 1 ? isPanning ? 'grabbing' : 'grab' : 'default'\n              },\n              onWheel: handleWheel,\n              onMouseDown: zoomLevel > 1 ? handlePanStart : undefined,\n              children: [imageError ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '400px',\n                  height: '300px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: '#ff6b6b',\n                  backgroundColor: '#2b2b2b'\n                },\n                children: \"\\u589E\\u5F3A\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n                ref: enhancedImageRef,\n                src: `http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,\n                alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n                onLoad: handleImageLoad,\n                onError: handleImageError,\n                style: {\n                  maxWidth: '100%',\n                  maxHeight: 'calc(100vh - 250px)',\n                  height: 'auto',\n                  display: 'block',\n                  opacity: imageLoaded ? 1 : 0.5,\n                  imageRendering: 'auto' // 增强图像使用高质量渲染\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 23\n              }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  width: `${splitPosition}%`,\n                  height: '100%',\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: originalImage,\n                  alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n                  style: {\n                    // 确保原图与增强图片尺寸完全匹配\n                    width: `${100 * 100 / splitPosition}%`,\n                    height: '100%',\n                    objectFit: 'fill',\n                    // 强制填充，实现最近邻插值效果\n                    imageRendering: 'pixelated',\n                    // 最近邻插值，保持像素清晰\n                    position: 'absolute',\n                    left: 0,\n                    top: 0\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 23\n              }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: 0,\n                  left: `${splitPosition}%`,\n                  width: '2px',\n                  height: '100%',\n                  backgroundColor: '#4a90e2',\n                  cursor: 'ew-resize',\n                  boxShadow: '0 0 4px rgba(74, 144, 226, 0.5)',\n                  transform: 'translateX(-1px)',\n                  zIndex: 10\n                },\n                onMouseDown: handleMouseDown,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    width: '16px',\n                    height: '32px',\n                    backgroundColor: '#4a90e2',\n                    borderRadius: '8px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '10px',\n                    fontWeight: 'bold',\n                    boxShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                  },\n                  children: \"\\u27F7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 23\n              }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '50%',\n                  left: '50%',\n                  transform: 'translate(-50%, -50%)',\n                  color: '#aaa',\n                  backgroundColor: 'rgba(43,43,43,0.9)',\n                  padding: '8px 12px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                },\n                children: \"\\u52A0\\u8F7D\\u4E2D...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      // 并排对比模式\n      _jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          padding: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '12px',\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u5E76\\u6392\\u5BF9\\u6BD4\\u6A21\\u5F0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '4px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: zoomOut,\n              disabled: zoomLevel <= 0.5,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                fontSize: '12px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              title: \"\\u7F29\\u5C0F\",\n              children: \"\\u2212\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                minWidth: '40px',\n                textAlign: 'center',\n                fontSize: '11px',\n                color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n              },\n              children: [(zoomLevel * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: zoomIn,\n              disabled: zoomLevel >= 5,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                color: zoomLevel >= 5 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                fontSize: '12px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              title: \"\\u653E\\u5927\",\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetZoom,\n              disabled: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? '#444' : '#555',\n                color: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? 'not-allowed' : 'pointer',\n                fontSize: '10px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              title: \"\\u91CD\\u7F6E\\u7F29\\u653E\",\n              children: \"\\u2302\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            gap: '16px',\n            alignItems: 'center',\n            justifyContent: 'center',\n            overflow: 'hidden'\n          },\n          children: [originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1',\n              textAlign: 'center',\n              maxWidth: '50%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                fontSize: '12px',\n                color: '#aaa'\n              },\n              children: \"\\u539F\\u59CB\\u56FE\\u50CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'inline-block',\n                transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                transformOrigin: 'center center',\n                transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                cursor: zoomLevel > 1 ? isPanning ? 'grabbing' : 'grab' : 'default'\n              },\n              onWheel: handleWheel,\n              onMouseDown: zoomLevel > 1 ? handlePanStart : undefined,\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: originalImage,\n                alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n                style: {\n                  maxWidth: '100%',\n                  maxHeight: 'calc(100vh - 300px)',\n                  height: 'auto',\n                  border: '1px solid #4a90e2',\n                  borderRadius: '4px',\n                  objectFit: 'contain'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1',\n              textAlign: 'center',\n              maxWidth: '50%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                fontSize: '12px',\n                color: '#aaa'\n              },\n              children: \"\\u589E\\u5F3A\\u56FE\\u50CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 15\n            }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: '200px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                border: '1px dashed #666',\n                borderRadius: '4px',\n                color: '#aaa',\n                backgroundColor: '#2b2b2b'\n              },\n              children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u56FE\\u50CF...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 17\n            }, this), imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: '200px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                border: '1px solid #ff6b6b',\n                borderRadius: '4px',\n                color: '#ff6b6b',\n                backgroundColor: '#2b2b2b'\n              },\n              children: \"\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: imageLoaded ? 'inline-block' : 'none',\n                transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                transformOrigin: 'center center',\n                transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                cursor: zoomLevel > 1 ? isPanning ? 'grabbing' : 'grab' : 'default'\n              },\n              onWheel: handleWheel,\n              onMouseDown: zoomLevel > 1 ? handlePanStart : undefined,\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,\n                alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n                onLoad: handleImageLoad,\n                onError: handleImageError,\n                style: {\n                  maxWidth: '100%',\n                  maxHeight: 'calc(100vh - 300px)',\n                  height: 'auto',\n                  border: '1px solid #28ca42',\n                  borderRadius: '4px',\n                  objectFit: 'contain'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '48px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        gap: '12px',\n        padding: '0 16px',\n        flexShrink: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: downloadImage,\n        style: {\n          padding: '6px 12px',\n          backgroundColor: '#28ca42',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer',\n          fontSize: '13px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '6px',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.target.style.backgroundColor = '#22a83a',\n        onMouseLeave: e => e.target.style.backgroundColor = '#28ca42',\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCE5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 13\n        }, this), \"\\u4E0B\\u8F7D\\u56FE\\u50CF\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 807,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: `http://localhost:8001/result/${result.enhanced_url}`,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        style: {\n          padding: '6px 12px',\n          backgroundColor: '#4a90e2',\n          color: 'white',\n          textDecoration: 'none',\n          borderRadius: '4px',\n          fontSize: '13px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '6px',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.target.style.backgroundColor = '#357abd',\n        onMouseLeave: e => e.target.style.backgroundColor = '#4a90e2',\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDD0D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 848,\n          columnNumber: 13\n        }, this), \"\\u65B0\\u7A97\\u53E3\\u67E5\\u770B\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 829,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#aaa',\n          marginLeft: 'auto'\n        },\n        children: [\"\\u6587\\u4EF6: \", result.filename]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 852,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 796,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultView, \"aHMVuXC9EAhFri4aNmdgG0SWmPI=\");\n_c = ResultView;\nexport default ResultView;\nvar _c;\n$RefreshReg$(_c, \"ResultView\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "ResultView", "result", "originalImage", "_s", "imageLoaded", "setImageLoaded", "imageError", "setImageError", "showParams", "setShowParams", "splitPosition", "setSplitPosition", "isDragging", "setIsDragging", "viewMode", "setViewMode", "zoomLevel", "setZoomLevel", "panOffset", "setPanOffset", "x", "y", "isPanning", "setIsPanning", "lastPanPoint", "setLastPanPoint", "containerRef", "enhancedImageRef", "imageContainerRef", "handleImageLoad", "handleImageError", "downloadImage", "link", "document", "createElement", "href", "enhanced_url", "download", "filename", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleMouseDown", "e", "preventDefault", "handleMouseMove", "current", "rect", "getBoundingClientRect", "clientX", "left", "percentage", "Math", "max", "min", "width", "handleMouseUp", "handleWheel", "delta", "deltaY", "newZoom", "handlePanStart", "clientY", "handlePanMove", "deltaX", "prev", "handlePanEnd", "resetZoom", "zoomIn", "zoomOut", "addEventListener", "removeEventListener", "resetSplit", "style", "height", "display", "flexDirection", "backgroundColor", "color", "children", "borderBottom", "alignItems", "justifyContent", "padding", "flexShrink", "gap", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "message", "onClick", "border", "cursor", "transition", "params_used", "scale", "use_realesrgan", "sharpening", "toFixed", "beauty", "onMouseEnter", "target", "onMouseLeave", "marginTop", "gridTemplateColumns", "denoising", "saturation", "contrast", "brightness", "flex", "overflow", "position", "marginBottom", "disabled", "title", "min<PERSON><PERSON><PERSON>", "textAlign", "ref", "userSelect", "maxHeight", "transform", "transform<PERSON><PERSON>in", "onWheel", "onMouseDown", "undefined", "src", "Date", "now", "alt", "onLoad", "onError", "max<PERSON><PERSON><PERSON>", "opacity", "imageRendering", "top", "objectFit", "boxShadow", "zIndex", "fontWeight", "borderTop", "rel", "textDecoration", "marginLeft", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\n\nconst ResultView = ({ result, originalImage }) => {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const [zoomLevel, setZoomLevel] = useState(1); // 缩放级别\n  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 }); // 平移偏移\n  const [isPanning, setIsPanning] = useState(false);\n  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });\n  const containerRef = useRef(null);\n  const enhancedImageRef = useRef(null);\n  const imageContainerRef = useRef(null);\n\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n    setImageError(false);\n  };\n\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleMouseDown = (e) => {\n    setIsDragging(true);\n    e.preventDefault();\n  };\n\n  const handleMouseMove = (e) => {\n    if (!isDragging || !containerRef.current) return;\n\n    const rect = containerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));\n    setSplitPosition(percentage);\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n    setIsPanning(false);\n  };\n\n  // 缩放处理\n  const handleWheel = (e) => {\n    if (!imageContainerRef.current) return;\n\n    e.preventDefault();\n    const delta = e.deltaY > 0 ? -0.1 : 0.1;\n    const newZoom = Math.max(0.5, Math.min(5, zoomLevel + delta));\n    setZoomLevel(newZoom);\n  };\n\n  // 平移处理\n  const handlePanStart = (e) => {\n    if (zoomLevel <= 1) return; // 只有放大时才允许平移\n\n    setIsPanning(true);\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n    e.preventDefault();\n  };\n\n  const handlePanMove = (e) => {\n    if (!isPanning) return;\n\n    const deltaX = e.clientX - lastPanPoint.x;\n    const deltaY = e.clientY - lastPanPoint.y;\n\n    setPanOffset(prev => ({\n      x: prev.x + deltaX,\n      y: prev.y + deltaY\n    }));\n\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n  };\n\n  const handlePanEnd = () => {\n    setIsPanning(false);\n  };\n\n  // 重置缩放和平移\n  const resetZoom = () => {\n    setZoomLevel(1);\n    setPanOffset({ x: 0, y: 0 });\n  };\n\n  // 缩放控制函数\n  const zoomIn = () => {\n    setZoomLevel(prev => Math.min(5, prev + 0.25));\n  };\n\n  const zoomOut = () => {\n    setZoomLevel(prev => Math.max(0.5, prev - 0.25));\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  // 添加平移事件监听\n  useEffect(() => {\n    if (isPanning) {\n      document.addEventListener('mousemove', handlePanMove);\n      document.addEventListener('mouseup', handlePanEnd);\n      return () => {\n        document.removeEventListener('mousemove', handlePanMove);\n        document.removeEventListener('mouseup', handlePanEnd);\n      };\n    }\n  }, [isPanning, lastPanPoint]);\n\n\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n\n  return (\n    <div style={{ \n      width: '100%', \n      height: '100%', \n      display: 'flex', \n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#ddd'\n    }}>\n      {/* 顶部工具栏 */}\n      <div style={{\n        height: '40px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        padding: '0 16px',\n        flexShrink: 0\n      }}>\n        {/* 左侧状态指示 */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div style={{\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }}></div>\n          <span style={{ fontSize: '13px', color: '#ddd' }}>处理完成</span>\n          {result.message && (\n            <span style={{ fontSize: '12px', color: '#aaa' }}>• {result.message}</span>\n          )}\n        </div>\n\n        {/* 右侧视图控制 */}\n        <div style={{ display: 'flex', gap: '6px', alignItems: 'center' }}>\n          <button\n            onClick={() => setViewMode('split')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '12px',\n              transition: 'background-color 0.2s'\n            }}\n          >\n            分割对比\n          </button>\n          <button\n            onClick={() => setViewMode('side-by-side')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '12px',\n              transition: 'background-color 0.2s'\n            }}\n          >\n            并排对比\n          </button>\n          {viewMode === 'split' && (\n            <button\n              onClick={resetSplit}\n              style={{\n                padding: '4px 8px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '12px',\n                transition: 'background-color 0.2s'\n              }}\n            >\n              重置\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* 参数信息栏 */}\n      {result.params_used && (\n        <div style={{\n          height: showParams ? 'auto' : '32px',\n          backgroundColor: '#2b2b2b',\n          borderBottom: '1px solid #555',\n          padding: '8px 16px',\n          flexShrink: 0,\n          transition: 'height 0.3s ease'\n        }}>\n          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n              <span style={{ fontSize: '12px', color: '#aaa' }}>处理参数:</span>\n              {!showParams && (\n                <div style={{ display: 'flex', gap: '12px', fontSize: '12px', color: '#ddd' }}>\n                  <span>{result.params_used.scale}x超分</span>\n                  <span>{result.params_used.use_realesrgan ? 'RealESRGAN' : '插值'}</span>\n                  <span>锐化{(result.params_used.sharpening * 100).toFixed(0)}%</span>\n                  <span>美颜{(result.params_used.beauty * 100).toFixed(0)}%</span>\n                </div>\n              )}\n            </div>\n            <button\n              onClick={() => setShowParams(!showParams)}\n              style={{\n                padding: '2px 6px',\n                backgroundColor: 'transparent',\n                color: '#aaa',\n                border: '1px solid #555',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px',\n                transition: 'all 0.2s'\n              }}\n              onMouseEnter={(e) => {\n                e.target.style.backgroundColor = '#555';\n                e.target.style.color = '#fff';\n              }}\n              onMouseLeave={(e) => {\n                e.target.style.backgroundColor = 'transparent';\n                e.target.style.color = '#aaa';\n              }}\n            >\n              {showParams ? '▲' : '▼'}\n            </button>\n          </div>\n\n          {showParams && (\n            <div style={{ \n              marginTop: '12px', \n              display: 'grid', \n              gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', \n              gap: '8px',\n              fontSize: '12px',\n              color: '#ddd'\n            }}>\n              <div><span style={{ color: '#aaa' }}>超分倍数:</span> {result.params_used.scale}x</div>\n              <div><span style={{ color: '#aaa' }}>AI模型:</span> {result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值'}</div>\n              <div><span style={{ color: '#aaa' }}>锐化:</span> {(result.params_used.sharpening * 100).toFixed(0)}%</div>\n              <div><span style={{ color: '#aaa' }}>降噪:</span> {result.params_used.denoising}</div>\n              <div><span style={{ color: '#aaa' }}>饱和度:</span> {result.params_used.saturation.toFixed(1)}</div>\n              <div><span style={{ color: '#aaa' }}>对比度:</span> {result.params_used.contrast.toFixed(1)}</div>\n              <div><span style={{ color: '#aaa' }}>亮度:</span> {result.params_used.brightness > 0 ? '+' : ''}{result.params_used.brightness}</div>\n              <div><span style={{ color: '#aaa' }}>美颜:</span> {(result.params_used.beauty * 100).toFixed(0)}%</div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* 主图像显示区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex', \n        flexDirection: 'column',\n        overflow: 'hidden',\n        position: 'relative'\n      }}>\n        {viewMode === 'split' ? (\n          // 分割线对比模式\n          <div style={{ \n            flex: 1, \n            display: 'flex', \n            flexDirection: 'column',\n            padding: '16px'\n          }}>\n            {/* 分割线信息栏和缩放控制 */}\n            <div style={{\n              height: '32px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              marginBottom: '12px',\n              fontSize: '12px',\n              color: '#aaa'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                <span>左侧：原始图像 | 右侧：增强图像 | 分割位置：{splitPosition.toFixed(0)}%</span>\n              </div>\n\n              {/* 缩放控制按钮 */}\n              <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>\n                <button\n                  onClick={zoomOut}\n                  disabled={zoomLevel <= 0.5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                    color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}\n                  title=\"缩小\"\n                >\n                  −\n                </button>\n\n                <span style={{\n                  minWidth: '40px',\n                  textAlign: 'center',\n                  fontSize: '11px',\n                  color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n                }}>\n                  {(zoomLevel * 100).toFixed(0)}%\n                </span>\n\n                <button\n                  onClick={zoomIn}\n                  disabled={zoomLevel >= 5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                    color: zoomLevel >= 5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}\n                  title=\"放大\"\n                >\n                  +\n                </button>\n\n                <button\n                  onClick={resetZoom}\n                  disabled={zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#444' : '#555',\n                    color: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? 'not-allowed' : 'pointer',\n                    fontSize: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}\n                  title=\"重置缩放\"\n                >\n                  ⌂\n                </button>\n              </div>\n            </div>\n\n            <div\n              ref={containerRef}\n              style={{\n                position: 'relative',\n                width: '100%',\n                height: '100%',\n                cursor: isDragging ? 'ew-resize' : 'default',\n                userSelect: 'none',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}\n            >\n              {originalImage && (\n                <div style={{ \n                  position: 'relative', \n                  width: '100%', \n                  height: '100%',\n                  maxHeight: 'calc(100vh - 200px)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>\n                  {/* 图像对比容器 - 支持缩放和平移 */}\n                  <div\n                    ref={imageContainerRef}\n                    style={{\n                      position: 'relative',\n                      display: 'inline-block',\n                      border: '1px solid #555',\n                      borderRadius: '4px',\n                      overflow: 'hidden',\n                      transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                      transformOrigin: 'center center',\n                      transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                      cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'\n                    }}\n                    onWheel={handleWheel}\n                    onMouseDown={zoomLevel > 1 ? handlePanStart : undefined}\n                  >\n                    {/* 增强图像作为背景 */}\n                    {imageError ? (\n                      <div style={{\n                        width: '400px',\n                        height: '300px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        color: '#ff6b6b',\n                        backgroundColor: '#2b2b2b'\n                      }}>\n                        增强图像加载失败\n                      </div>\n                    ) : (\n                      <img\n                        ref={enhancedImageRef}\n                        src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                        alt=\"增强图像\"\n                        onLoad={handleImageLoad}\n                        onError={handleImageError}\n                        style={{\n                          maxWidth: '100%',\n                          maxHeight: 'calc(100vh - 250px)',\n                          height: 'auto',\n                          display: 'block',\n                          opacity: imageLoaded ? 1 : 0.5,\n                          imageRendering: 'auto' // 增强图像使用高质量渲染\n                        }}\n                      />\n                    )}\n\n                    {/* 原始图像覆盖层 - 像素级对齐 */}\n                    {imageLoaded && (\n                      <div\n                        style={{\n                          position: 'absolute',\n                          top: 0,\n                          left: 0,\n                          width: `${splitPosition}%`,\n                          height: '100%',\n                          overflow: 'hidden'\n                        }}\n                      >\n                        <img\n                          src={originalImage}\n                          alt=\"原始图像\"\n                          style={{\n                            // 确保原图与增强图片尺寸完全匹配\n                            width: `${100 * 100 / splitPosition}%`,\n                            height: '100%',\n                            objectFit: 'fill', // 强制填充，实现最近邻插值效果\n                            imageRendering: 'pixelated', // 最近邻插值，保持像素清晰\n                            position: 'absolute',\n                            left: 0,\n                            top: 0\n                          }}\n                        />\n                      </div>\n                    )}\n\n                    {/* 分割线 */}\n                    {imageLoaded && (\n                      <div\n                        style={{\n                          position: 'absolute',\n                          top: 0,\n                          left: `${splitPosition}%`,\n                          width: '2px',\n                          height: '100%',\n                          backgroundColor: '#4a90e2',\n                          cursor: 'ew-resize',\n                          boxShadow: '0 0 4px rgba(74, 144, 226, 0.5)',\n                          transform: 'translateX(-1px)',\n                          zIndex: 10\n                        }}\n                        onMouseDown={handleMouseDown}\n                      >\n                        {/* 分割线手柄 */}\n                        <div\n                          style={{\n                            position: 'absolute',\n                            top: '50%',\n                            left: '50%',\n                            transform: 'translate(-50%, -50%)',\n                            width: '16px',\n                            height: '32px',\n                            backgroundColor: '#4a90e2',\n                            borderRadius: '8px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            color: 'white',\n                            fontSize: '10px',\n                            fontWeight: 'bold',\n                            boxShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                          }}\n                        >\n                          ⟷\n                        </div>\n                      </div>\n                    )}\n\n                    {/* 加载状态 */}\n                    {!imageLoaded && !imageError && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '50%',\n                        left: '50%',\n                        transform: 'translate(-50%, -50%)',\n                        color: '#aaa',\n                        backgroundColor: 'rgba(43,43,43,0.9)',\n                        padding: '8px 12px',\n                        borderRadius: '4px',\n                        fontSize: '12px'\n                      }}>\n                        加载中...\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        ) : (\n          // 并排对比模式\n          <div style={{\n            flex: 1,\n            display: 'flex',\n            flexDirection: 'column',\n            padding: '16px'\n          }}>\n            {/* 并排对比控制栏 */}\n            <div style={{\n              height: '32px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              marginBottom: '12px',\n              fontSize: '12px',\n              color: '#aaa'\n            }}>\n              <span>并排对比模式</span>\n\n              {/* 缩放控制按钮 */}\n              <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>\n                <button\n                  onClick={zoomOut}\n                  disabled={zoomLevel <= 0.5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                    color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}\n                  title=\"缩小\"\n                >\n                  −\n                </button>\n\n                <span style={{\n                  minWidth: '40px',\n                  textAlign: 'center',\n                  fontSize: '11px',\n                  color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n                }}>\n                  {(zoomLevel * 100).toFixed(0)}%\n                </span>\n\n                <button\n                  onClick={zoomIn}\n                  disabled={zoomLevel >= 5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                    color: zoomLevel >= 5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}\n                  title=\"放大\"\n                >\n                  +\n                </button>\n\n                <button\n                  onClick={resetZoom}\n                  disabled={zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#444' : '#555',\n                    color: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? 'not-allowed' : 'pointer',\n                    fontSize: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}\n                  title=\"重置缩放\"\n                >\n                  ⌂\n                </button>\n              </div>\n            </div>\n\n            {/* 并排图像容器 */}\n            <div style={{\n              flex: 1,\n              display: 'flex',\n              gap: '16px',\n              alignItems: 'center',\n              justifyContent: 'center',\n              overflow: 'hidden'\n            }}>\n            {/* 原始图像 */}\n            {originalImage && (\n              <div style={{ \n                flex: '1', \n                textAlign: 'center',\n                maxWidth: '50%'\n              }}>\n                <div style={{ \n                  marginBottom: '8px', \n                  fontSize: '12px', \n                  color: '#aaa' \n                }}>\n                  原始图像\n                </div>\n                <div\n                  style={{\n                    display: 'inline-block',\n                    transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                    transformOrigin: 'center center',\n                    transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                    cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'\n                  }}\n                  onWheel={handleWheel}\n                  onMouseDown={zoomLevel > 1 ? handlePanStart : undefined}\n                >\n                  <img\n                    src={originalImage}\n                    alt=\"原始图像\"\n                    style={{\n                      maxWidth: '100%',\n                      maxHeight: 'calc(100vh - 300px)',\n                      height: 'auto',\n                      border: '1px solid #4a90e2',\n                      borderRadius: '4px',\n                      objectFit: 'contain'\n                    }}\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* 增强图像 */}\n            <div style={{ \n              flex: '1', \n              textAlign: 'center',\n              maxWidth: '50%'\n            }}>\n              <div style={{ \n                marginBottom: '8px', \n                fontSize: '12px', \n                color: '#aaa' \n              }}>\n                增强图像\n              </div>\n\n              {!imageLoaded && !imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '200px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px dashed #666',\n                  borderRadius: '4px',\n                  color: '#aaa',\n                  backgroundColor: '#2b2b2b'\n                }}>\n                  正在加载图像...\n                </div>\n              )}\n\n              {imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '200px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px solid #ff6b6b',\n                  borderRadius: '4px',\n                  color: '#ff6b6b',\n                  backgroundColor: '#2b2b2b'\n                }}>\n                  图像加载失败\n                </div>\n              )}\n\n              <div\n                style={{\n                  display: imageLoaded ? 'inline-block' : 'none',\n                  transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                  transformOrigin: 'center center',\n                  transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                  cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'\n                }}\n                onWheel={handleWheel}\n                onMouseDown={zoomLevel > 1 ? handlePanStart : undefined}\n              >\n                <img\n                  src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                  alt=\"增强图像\"\n                  onLoad={handleImageLoad}\n                  onError={handleImageError}\n                  style={{\n                    maxWidth: '100%',\n                    maxHeight: 'calc(100vh - 300px)',\n                    height: 'auto',\n                    border: '1px solid #28ca42',\n                    borderRadius: '4px',\n                    objectFit: 'contain'\n                  }}\n                />\n              </div>\n            </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 底部控制栏 */}\n      {imageLoaded && (\n        <div style={{\n          height: '48px',\n          backgroundColor: '#333',\n          borderTop: '1px solid #555',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '12px',\n          padding: '0 16px',\n          flexShrink: 0\n        }}>\n          <button\n            onClick={downloadImage}\n            style={{\n              padding: '6px 12px',\n              backgroundColor: '#28ca42',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '13px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px',\n              transition: 'background-color 0.2s'\n            }}\n            onMouseEnter={(e) => e.target.style.backgroundColor = '#22a83a'}\n            onMouseLeave={(e) => e.target.style.backgroundColor = '#28ca42'}\n          >\n            <span>📥</span>\n            下载图像\n          </button>\n\n          <a\n            href={`http://localhost:8001/result/${result.enhanced_url}`}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            style={{\n              padding: '6px 12px',\n              backgroundColor: '#4a90e2',\n              color: 'white',\n              textDecoration: 'none',\n              borderRadius: '4px',\n              fontSize: '13px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px',\n              transition: 'background-color 0.2s'\n            }}\n            onMouseEnter={(e) => e.target.style.backgroundColor = '#357abd'}\n            onMouseLeave={(e) => e.target.style.backgroundColor = '#4a90e2'}\n          >\n            <span>🔍</span>\n            新窗口查看\n          </a>\n\n          <div style={{\n            fontSize: '12px',\n            color: '#aaa',\n            marginLeft: 'auto'\n          }}>\n            文件: {result.filename}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ResultView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,UAAU,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC;IAAEyB,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC;IAAEyB,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAChE,MAAMK,YAAY,GAAG9B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM+B,gBAAgB,GAAG/B,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMgC,iBAAiB,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAEtC,MAAMiC,eAAe,GAAGA,CAAA,KAAM;IAC5BxB,cAAc,CAAC,IAAI,CAAC;IACpBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMuB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BvB,aAAa,CAAC,IAAI,CAAC;IACnBF,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,gCAAgClC,MAAM,CAACmC,YAAY,EAAE;IACjEJ,IAAI,CAACK,QAAQ,GAAG,YAAYpC,MAAM,CAACqC,QAAQ,EAAE;IAC7CL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;IAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;IACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMW,eAAe,GAAIC,CAAC,IAAK;IAC7B/B,aAAa,CAAC,IAAI,CAAC;IACnB+B,CAAC,CAACC,cAAc,CAAC,CAAC;EACpB,CAAC;EAED,MAAMC,eAAe,GAAIF,CAAC,IAAK;IAC7B,IAAI,CAAChC,UAAU,IAAI,CAACc,YAAY,CAACqB,OAAO,EAAE;IAE1C,MAAMC,IAAI,GAAGtB,YAAY,CAACqB,OAAO,CAACE,qBAAqB,CAAC,CAAC;IACzD,MAAM7B,CAAC,GAAGwB,CAAC,CAACM,OAAO,GAAGF,IAAI,CAACG,IAAI;IAC/B,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAGnC,CAAC,GAAG4B,IAAI,CAACQ,KAAK,GAAI,GAAG,CAAC,CAAC;IACrE7C,gBAAgB,CAACyC,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1B5C,aAAa,CAAC,KAAK,CAAC;IACpBU,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMmC,WAAW,GAAId,CAAC,IAAK;IACzB,IAAI,CAAChB,iBAAiB,CAACmB,OAAO,EAAE;IAEhCH,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMc,KAAK,GAAGf,CAAC,CAACgB,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG;IACvC,MAAMC,OAAO,GAAGR,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEvC,SAAS,GAAG2C,KAAK,CAAC,CAAC;IAC7D1C,YAAY,CAAC4C,OAAO,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIlB,CAAC,IAAK;IAC5B,IAAI5B,SAAS,IAAI,CAAC,EAAE,OAAO,CAAC;;IAE5BO,YAAY,CAAC,IAAI,CAAC;IAClBE,eAAe,CAAC;MACdL,CAAC,EAAEwB,CAAC,CAACM,OAAO;MACZ7B,CAAC,EAAEuB,CAAC,CAACmB;IACP,CAAC,CAAC;IACFnB,CAAC,CAACC,cAAc,CAAC,CAAC;EACpB,CAAC;EAED,MAAMmB,aAAa,GAAIpB,CAAC,IAAK;IAC3B,IAAI,CAACtB,SAAS,EAAE;IAEhB,MAAM2C,MAAM,GAAGrB,CAAC,CAACM,OAAO,GAAG1B,YAAY,CAACJ,CAAC;IACzC,MAAMwC,MAAM,GAAGhB,CAAC,CAACmB,OAAO,GAAGvC,YAAY,CAACH,CAAC;IAEzCF,YAAY,CAAC+C,IAAI,KAAK;MACpB9C,CAAC,EAAE8C,IAAI,CAAC9C,CAAC,GAAG6C,MAAM;MAClB5C,CAAC,EAAE6C,IAAI,CAAC7C,CAAC,GAAGuC;IACd,CAAC,CAAC,CAAC;IAEHnC,eAAe,CAAC;MACdL,CAAC,EAAEwB,CAAC,CAACM,OAAO;MACZ7B,CAAC,EAAEuB,CAAC,CAACmB;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB5C,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAM6C,SAAS,GAAGA,CAAA,KAAM;IACtBnD,YAAY,CAAC,CAAC,CAAC;IACfE,YAAY,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMgD,MAAM,GAAGA,CAAA,KAAM;IACnBpD,YAAY,CAACiD,IAAI,IAAIb,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEW,IAAI,GAAG,IAAI,CAAC,CAAC;EAChD,CAAC;EAED,MAAMI,OAAO,GAAGA,CAAA,KAAM;IACpBrD,YAAY,CAACiD,IAAI,IAAIb,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEY,IAAI,GAAG,IAAI,CAAC,CAAC;EAClD,CAAC;;EAED;EACArE,SAAS,CAAC,MAAM;IACd,IAAIe,UAAU,EAAE;MACdqB,QAAQ,CAACsC,gBAAgB,CAAC,WAAW,EAAEzB,eAAe,CAAC;MACvDb,QAAQ,CAACsC,gBAAgB,CAAC,SAAS,EAAEd,aAAa,CAAC;MACnD,OAAO,MAAM;QACXxB,QAAQ,CAACuC,mBAAmB,CAAC,WAAW,EAAE1B,eAAe,CAAC;QAC1Db,QAAQ,CAACuC,mBAAmB,CAAC,SAAS,EAAEf,aAAa,CAAC;MACxD,CAAC;IACH;EACF,CAAC,EAAE,CAAC7C,UAAU,CAAC,CAAC;;EAEhB;EACAf,SAAS,CAAC,MAAM;IACd,IAAIyB,SAAS,EAAE;MACbW,QAAQ,CAACsC,gBAAgB,CAAC,WAAW,EAAEP,aAAa,CAAC;MACrD/B,QAAQ,CAACsC,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MAClD,OAAO,MAAM;QACXlC,QAAQ,CAACuC,mBAAmB,CAAC,WAAW,EAAER,aAAa,CAAC;QACxD/B,QAAQ,CAACuC,mBAAmB,CAAC,SAAS,EAAEL,YAAY,CAAC;MACvD,CAAC;IACH;EACF,CAAC,EAAE,CAAC7C,SAAS,EAAEE,YAAY,CAAC,CAAC;;EAI7B;EACA,MAAMiD,UAAU,GAAGA,CAAA,KAAM;IACvB9D,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,oBACEZ,OAAA;IAAK2E,KAAK,EAAE;MACVlB,KAAK,EAAE,MAAM;MACbmB,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,eAAe,EAAE,SAAS;MAC1BC,KAAK,EAAE;IACT,CAAE;IAAAC,QAAA,gBAEAjF,OAAA;MAAK2E,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdG,eAAe,EAAE,SAAS;QAC1BG,YAAY,EAAE,gBAAgB;QAC9BL,OAAO,EAAE,MAAM;QACfM,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BC,OAAO,EAAE,QAAQ;QACjBC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,gBAEAjF,OAAA;QAAK2E,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEM,UAAU,EAAE,QAAQ;UAAEI,GAAG,EAAE;QAAO,CAAE;QAAAN,QAAA,gBACjEjF,OAAA;UAAK2E,KAAK,EAAE;YACVlB,KAAK,EAAE,KAAK;YACZmB,MAAM,EAAE,KAAK;YACbY,YAAY,EAAE,KAAK;YACnBT,eAAe,EAAE;UACnB;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACT5F,OAAA;UAAM2E,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAEb,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,EAAC;QAAI;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC5D1F,MAAM,CAAC4F,OAAO,iBACb9F,OAAA;UAAM2E,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAEb,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,GAAC,SAAE,EAAC/E,MAAM,CAAC4F,OAAO;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC3E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN5F,OAAA;QAAK2E,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEU,GAAG,EAAE,KAAK;UAAEJ,UAAU,EAAE;QAAS,CAAE;QAAAF,QAAA,gBAChEjF,OAAA;UACE+F,OAAO,EAAEA,CAAA,KAAM/E,WAAW,CAAC,OAAO,CAAE;UACpC2D,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAEhE,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,MAAM;YAC1DiE,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5F,OAAA;UACE+F,OAAO,EAAEA,CAAA,KAAM/E,WAAW,CAAC,cAAc,CAAE;UAC3C2D,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAEhE,QAAQ,KAAK,cAAc,GAAG,SAAS,GAAG,MAAM;YACjEiE,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACR7E,QAAQ,KAAK,OAAO,iBACnBf,OAAA;UACE+F,OAAO,EAAErB,UAAW;UACpBC,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL1F,MAAM,CAACiG,WAAW,iBACjBnG,OAAA;MAAK2E,KAAK,EAAE;QACVC,MAAM,EAAEnE,UAAU,GAAG,MAAM,GAAG,MAAM;QACpCsE,eAAe,EAAE,SAAS;QAC1BG,YAAY,EAAE,gBAAgB;QAC9BG,OAAO,EAAE,UAAU;QACnBC,UAAU,EAAE,CAAC;QACbY,UAAU,EAAE;MACd,CAAE;MAAAjB,QAAA,gBACAjF,OAAA;QAAK2E,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEM,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAH,QAAA,gBACrFjF,OAAA;UAAK2E,KAAK,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEM,UAAU,EAAE,QAAQ;YAAEI,GAAG,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACjEjF,OAAA;YAAM2E,KAAK,EAAE;cAAEkB,QAAQ,EAAE,MAAM;cAAEb,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC7D,CAACnF,UAAU,iBACVT,OAAA;YAAK2E,KAAK,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAEU,GAAG,EAAE,MAAM;cAAEM,QAAQ,EAAE,MAAM;cAAEb,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,gBAC5EjF,OAAA;cAAAiF,QAAA,GAAO/E,MAAM,CAACiG,WAAW,CAACC,KAAK,EAAC,eAAG;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1C5F,OAAA;cAAAiF,QAAA,EAAO/E,MAAM,CAACiG,WAAW,CAACE,cAAc,GAAG,YAAY,GAAG;YAAI;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtE5F,OAAA;cAAAiF,QAAA,GAAM,cAAE,EAAC,CAAC/E,MAAM,CAACiG,WAAW,CAACG,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClE5F,OAAA;cAAAiF,QAAA,GAAM,cAAE,EAAC,CAAC/E,MAAM,CAACiG,WAAW,CAACK,MAAM,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN5F,OAAA;UACE+F,OAAO,EAAEA,CAAA,KAAMrF,aAAa,CAAC,CAACD,UAAU,CAAE;UAC1CkE,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAE,aAAa;YAC9BC,KAAK,EAAE,MAAM;YACbgB,MAAM,EAAE,gBAAgB;YACxBR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UACFO,YAAY,EAAG5D,CAAC,IAAK;YACnBA,CAAC,CAAC6D,MAAM,CAAC/B,KAAK,CAACI,eAAe,GAAG,MAAM;YACvClC,CAAC,CAAC6D,MAAM,CAAC/B,KAAK,CAACK,KAAK,GAAG,MAAM;UAC/B,CAAE;UACF2B,YAAY,EAAG9D,CAAC,IAAK;YACnBA,CAAC,CAAC6D,MAAM,CAAC/B,KAAK,CAACI,eAAe,GAAG,aAAa;YAC9ClC,CAAC,CAAC6D,MAAM,CAAC/B,KAAK,CAACK,KAAK,GAAG,MAAM;UAC/B,CAAE;UAAAC,QAAA,EAEDxE,UAAU,GAAG,GAAG,GAAG;QAAG;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELnF,UAAU,iBACTT,OAAA;QAAK2E,KAAK,EAAE;UACViC,SAAS,EAAE,MAAM;UACjB/B,OAAO,EAAE,MAAM;UACfgC,mBAAmB,EAAE,sCAAsC;UAC3DtB,GAAG,EAAE,KAAK;UACVM,QAAQ,EAAE,MAAM;UAChBb,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,gBACAjF,OAAA;UAAAiF,QAAA,gBAAKjF,OAAA;YAAM2E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC1F,MAAM,CAACiG,WAAW,CAACC,KAAK,EAAC,GAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnF5F,OAAA;UAAAiF,QAAA,gBAAKjF,OAAA;YAAM2E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC1F,MAAM,CAACiG,WAAW,CAACE,cAAc,GAAG,YAAY,GAAG,MAAM;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnH5F,OAAA;UAAAiF,QAAA,gBAAKjF,OAAA;YAAM2E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC,CAAC1F,MAAM,CAACiG,WAAW,CAACG,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzG5F,OAAA;UAAAiF,QAAA,gBAAKjF,OAAA;YAAM2E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC1F,MAAM,CAACiG,WAAW,CAACW,SAAS;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpF5F,OAAA;UAAAiF,QAAA,gBAAKjF,OAAA;YAAM2E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC1F,MAAM,CAACiG,WAAW,CAACY,UAAU,CAACR,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjG5F,OAAA;UAAAiF,QAAA,gBAAKjF,OAAA;YAAM2E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC1F,MAAM,CAACiG,WAAW,CAACa,QAAQ,CAACT,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/F5F,OAAA;UAAAiF,QAAA,gBAAKjF,OAAA;YAAM2E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC1F,MAAM,CAACiG,WAAW,CAACc,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE/G,MAAM,CAACiG,WAAW,CAACc,UAAU;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnI5F,OAAA;UAAAiF,QAAA,gBAAKjF,OAAA;YAAM2E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC,CAAC1F,MAAM,CAACiG,WAAW,CAACK,MAAM,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClG,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGD5F,OAAA;MAAK2E,KAAK,EAAE;QACVuC,IAAI,EAAE,CAAC;QACPrC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBqC,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE;MACZ,CAAE;MAAAnC,QAAA,EACClE,QAAQ,KAAK,OAAO;MAAA;MACnB;MACAf,OAAA;QAAK2E,KAAK,EAAE;UACVuC,IAAI,EAAE,CAAC;UACPrC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBO,OAAO,EAAE;QACX,CAAE;QAAAJ,QAAA,gBAEAjF,OAAA;UAAK2E,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,eAAe;YAC/BiC,YAAY,EAAE,MAAM;YACpBxB,QAAQ,EAAE,MAAM;YAChBb,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,gBACAjF,OAAA;YAAK2E,KAAK,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAEM,UAAU,EAAE,QAAQ;cAAEI,GAAG,EAAE;YAAM,CAAE;YAAAN,QAAA,eAChEjF,OAAA;cAAAiF,QAAA,GAAM,0HAAyB,EAACtE,aAAa,CAAC4F,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAGN5F,OAAA;YAAK2E,KAAK,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAEM,UAAU,EAAE,QAAQ;cAAEI,GAAG,EAAE;YAAM,CAAE;YAAAN,QAAA,gBAChEjF,OAAA;cACE+F,OAAO,EAAExB,OAAQ;cACjB+C,QAAQ,EAAErG,SAAS,IAAI,GAAI;cAC3B0D,KAAK,EAAE;gBACLlB,KAAK,EAAE,MAAM;gBACbmB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAE9D,SAAS,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACnD+D,KAAK,EAAE/D,SAAS,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACzC+E,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAEhF,SAAS,IAAI,GAAG,GAAG,aAAa,GAAG,SAAS;gBACpD4E,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE;cAClB,CAAE;cACFmC,KAAK,EAAC,cAAI;cAAAtC,QAAA,EACX;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET5F,OAAA;cAAM2E,KAAK,EAAE;gBACX6C,QAAQ,EAAE,MAAM;gBAChBC,SAAS,EAAE,QAAQ;gBACnB5B,QAAQ,EAAE,MAAM;gBAChBb,KAAK,EAAE/D,SAAS,KAAK,CAAC,GAAG,MAAM,GAAG;cACpC,CAAE;cAAAgE,QAAA,GACC,CAAChE,SAAS,GAAG,GAAG,EAAEsF,OAAO,CAAC,CAAC,CAAC,EAAC,GAChC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAEP5F,OAAA;cACE+F,OAAO,EAAEzB,MAAO;cAChBgD,QAAQ,EAAErG,SAAS,IAAI,CAAE;cACzB0D,KAAK,EAAE;gBACLlB,KAAK,EAAE,MAAM;gBACbmB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAE9D,SAAS,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM;gBACjD+D,KAAK,EAAE/D,SAAS,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM;gBACvC+E,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAEhF,SAAS,IAAI,CAAC,GAAG,aAAa,GAAG,SAAS;gBAClD4E,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE;cAClB,CAAE;cACFmC,KAAK,EAAC,cAAI;cAAAtC,QAAA,EACX;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET5F,OAAA;cACE+F,OAAO,EAAE1B,SAAU;cACnBiD,QAAQ,EAAErG,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAE;cACpEqD,KAAK,EAAE;gBACLlB,KAAK,EAAE,MAAM;gBACbmB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAG9D,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,MAAM,GAAG,MAAM;gBAC9F0D,KAAK,EAAG/D,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,MAAM,GAAG,MAAM;gBACpF0E,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAGhF,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,aAAa,GAAG,SAAS;gBAC/FuE,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE;cAClB,CAAE;cACFmC,KAAK,EAAC,0BAAM;cAAAtC,QAAA,EACb;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5F,OAAA;UACE0H,GAAG,EAAE/F,YAAa;UAClBgD,KAAK,EAAE;YACLyC,QAAQ,EAAE,UAAU;YACpB3D,KAAK,EAAE,MAAM;YACbmB,MAAM,EAAE,MAAM;YACdqB,MAAM,EAAEpF,UAAU,GAAG,WAAW,GAAG,SAAS;YAC5C8G,UAAU,EAAE,MAAM;YAClB9C,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAH,QAAA,EAED9E,aAAa,iBACZH,OAAA;YAAK2E,KAAK,EAAE;cACVyC,QAAQ,EAAE,UAAU;cACpB3D,KAAK,EAAE,MAAM;cACbmB,MAAM,EAAE,MAAM;cACdgD,SAAS,EAAE,qBAAqB;cAChC/C,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAH,QAAA,eAEAjF,OAAA;cACE0H,GAAG,EAAE7F,iBAAkB;cACvB8C,KAAK,EAAE;gBACLyC,QAAQ,EAAE,UAAU;gBACpBvC,OAAO,EAAE,cAAc;gBACvBmB,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnB2B,QAAQ,EAAE,QAAQ;gBAClBU,SAAS,EAAE,SAAS5G,SAAS,eAAeE,SAAS,CAACE,CAAC,GAAGJ,SAAS,OAAOE,SAAS,CAACG,CAAC,GAAGL,SAAS,KAAK;gBACtG6G,eAAe,EAAE,eAAe;gBAChC5B,UAAU,EAAE3E,SAAS,GAAG,MAAM,GAAG,yBAAyB;gBAC1D0E,MAAM,EAAEhF,SAAS,GAAG,CAAC,GAAIM,SAAS,GAAG,UAAU,GAAG,MAAM,GAAI;cAC9D,CAAE;cACFwG,OAAO,EAAEpE,WAAY;cACrBqE,WAAW,EAAE/G,SAAS,GAAG,CAAC,GAAG8C,cAAc,GAAGkE,SAAU;cAAAhD,QAAA,GAGvD1E,UAAU,gBACTP,OAAA;gBAAK2E,KAAK,EAAE;kBACVlB,KAAK,EAAE,OAAO;kBACdmB,MAAM,EAAE,OAAO;kBACfC,OAAO,EAAE,MAAM;kBACfM,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE,QAAQ;kBACxBJ,KAAK,EAAE,SAAS;kBAChBD,eAAe,EAAE;gBACnB,CAAE;gBAAAE,QAAA,EAAC;cAEH;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gBAEN5F,OAAA;gBACE0H,GAAG,EAAE9F,gBAAiB;gBACtBsG,GAAG,EAAE,gCAAgChI,MAAM,CAACmC,YAAY,MAAM8F,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;gBAC3EC,GAAG,EAAC,0BAAM;gBACVC,MAAM,EAAExG,eAAgB;gBACxByG,OAAO,EAAExG,gBAAiB;gBAC1B4C,KAAK,EAAE;kBACL6D,QAAQ,EAAE,MAAM;kBAChBZ,SAAS,EAAE,qBAAqB;kBAChChD,MAAM,EAAE,MAAM;kBACdC,OAAO,EAAE,OAAO;kBAChB4D,OAAO,EAAEpI,WAAW,GAAG,CAAC,GAAG,GAAG;kBAC9BqI,cAAc,EAAE,MAAM,CAAC;gBACzB;cAAE;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,EAGAvF,WAAW,iBACVL,OAAA;gBACE2E,KAAK,EAAE;kBACLyC,QAAQ,EAAE,UAAU;kBACpBuB,GAAG,EAAE,CAAC;kBACNvF,IAAI,EAAE,CAAC;kBACPK,KAAK,EAAE,GAAG9C,aAAa,GAAG;kBAC1BiE,MAAM,EAAE,MAAM;kBACduC,QAAQ,EAAE;gBACZ,CAAE;gBAAAlC,QAAA,eAEFjF,OAAA;kBACEkI,GAAG,EAAE/H,aAAc;kBACnBkI,GAAG,EAAC,0BAAM;kBACV1D,KAAK,EAAE;oBACL;oBACAlB,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG9C,aAAa,GAAG;oBACtCiE,MAAM,EAAE,MAAM;oBACdgE,SAAS,EAAE,MAAM;oBAAE;oBACnBF,cAAc,EAAE,WAAW;oBAAE;oBAC7BtB,QAAQ,EAAE,UAAU;oBACpBhE,IAAI,EAAE,CAAC;oBACPuF,GAAG,EAAE;kBACP;gBAAE;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,EAGAvF,WAAW,iBACVL,OAAA;gBACE2E,KAAK,EAAE;kBACLyC,QAAQ,EAAE,UAAU;kBACpBuB,GAAG,EAAE,CAAC;kBACNvF,IAAI,EAAE,GAAGzC,aAAa,GAAG;kBACzB8C,KAAK,EAAE,KAAK;kBACZmB,MAAM,EAAE,MAAM;kBACdG,eAAe,EAAE,SAAS;kBAC1BkB,MAAM,EAAE,WAAW;kBACnB4C,SAAS,EAAE,iCAAiC;kBAC5ChB,SAAS,EAAE,kBAAkB;kBAC7BiB,MAAM,EAAE;gBACV,CAAE;gBACFd,WAAW,EAAEpF,eAAgB;gBAAAqC,QAAA,eAG7BjF,OAAA;kBACE2E,KAAK,EAAE;oBACLyC,QAAQ,EAAE,UAAU;oBACpBuB,GAAG,EAAE,KAAK;oBACVvF,IAAI,EAAE,KAAK;oBACXyE,SAAS,EAAE,uBAAuB;oBAClCpE,KAAK,EAAE,MAAM;oBACbmB,MAAM,EAAE,MAAM;oBACdG,eAAe,EAAE,SAAS;oBAC1BS,YAAY,EAAE,KAAK;oBACnBX,OAAO,EAAE,MAAM;oBACfM,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,QAAQ;oBACxBJ,KAAK,EAAE,OAAO;oBACda,QAAQ,EAAE,MAAM;oBAChBkD,UAAU,EAAE,MAAM;oBAClBF,SAAS,EAAE;kBACb,CAAE;kBAAA5D,QAAA,EACH;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGA,CAACvF,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;gBAAK2E,KAAK,EAAE;kBACVyC,QAAQ,EAAE,UAAU;kBACpBuB,GAAG,EAAE,KAAK;kBACVvF,IAAI,EAAE,KAAK;kBACXyE,SAAS,EAAE,uBAAuB;kBAClC7C,KAAK,EAAE,MAAM;kBACbD,eAAe,EAAE,oBAAoB;kBACrCM,OAAO,EAAE,UAAU;kBACnBG,YAAY,EAAE,KAAK;kBACnBK,QAAQ,EAAE;gBACZ,CAAE;gBAAAZ,QAAA,EAAC;cAEH;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;MAAA;MAEN;MACA5F,OAAA;QAAK2E,KAAK,EAAE;UACVuC,IAAI,EAAE,CAAC;UACPrC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBO,OAAO,EAAE;QACX,CAAE;QAAAJ,QAAA,gBAEAjF,OAAA;UAAK2E,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,eAAe;YAC/BiC,YAAY,EAAE,MAAM;YACpBxB,QAAQ,EAAE,MAAM;YAChBb,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,gBACAjF,OAAA;YAAAiF,QAAA,EAAM;UAAM;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGnB5F,OAAA;YAAK2E,KAAK,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAEM,UAAU,EAAE,QAAQ;cAAEI,GAAG,EAAE;YAAM,CAAE;YAAAN,QAAA,gBAChEjF,OAAA;cACE+F,OAAO,EAAExB,OAAQ;cACjB+C,QAAQ,EAAErG,SAAS,IAAI,GAAI;cAC3B0D,KAAK,EAAE;gBACLlB,KAAK,EAAE,MAAM;gBACbmB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAE9D,SAAS,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACnD+D,KAAK,EAAE/D,SAAS,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACzC+E,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAEhF,SAAS,IAAI,GAAG,GAAG,aAAa,GAAG,SAAS;gBACpD4E,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE;cAClB,CAAE;cACFmC,KAAK,EAAC,cAAI;cAAAtC,QAAA,EACX;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET5F,OAAA;cAAM2E,KAAK,EAAE;gBACX6C,QAAQ,EAAE,MAAM;gBAChBC,SAAS,EAAE,QAAQ;gBACnB5B,QAAQ,EAAE,MAAM;gBAChBb,KAAK,EAAE/D,SAAS,KAAK,CAAC,GAAG,MAAM,GAAG;cACpC,CAAE;cAAAgE,QAAA,GACC,CAAChE,SAAS,GAAG,GAAG,EAAEsF,OAAO,CAAC,CAAC,CAAC,EAAC,GAChC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAEP5F,OAAA;cACE+F,OAAO,EAAEzB,MAAO;cAChBgD,QAAQ,EAAErG,SAAS,IAAI,CAAE;cACzB0D,KAAK,EAAE;gBACLlB,KAAK,EAAE,MAAM;gBACbmB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAE9D,SAAS,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM;gBACjD+D,KAAK,EAAE/D,SAAS,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM;gBACvC+E,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAEhF,SAAS,IAAI,CAAC,GAAG,aAAa,GAAG,SAAS;gBAClD4E,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE;cAClB,CAAE;cACFmC,KAAK,EAAC,cAAI;cAAAtC,QAAA,EACX;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET5F,OAAA;cACE+F,OAAO,EAAE1B,SAAU;cACnBiD,QAAQ,EAAErG,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAE;cACpEqD,KAAK,EAAE;gBACLlB,KAAK,EAAE,MAAM;gBACbmB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAG9D,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,MAAM,GAAG,MAAM;gBAC9F0D,KAAK,EAAG/D,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,MAAM,GAAG,MAAM;gBACpF0E,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAGhF,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,aAAa,GAAG,SAAS;gBAC/FuE,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE;cAClB,CAAE;cACFmC,KAAK,EAAC,0BAAM;cAAAtC,QAAA,EACb;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5F,OAAA;UAAK2E,KAAK,EAAE;YACVuC,IAAI,EAAE,CAAC;YACPrC,OAAO,EAAE,MAAM;YACfU,GAAG,EAAE,MAAM;YACXJ,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxB+B,QAAQ,EAAE;UACZ,CAAE;UAAAlC,QAAA,GAED9E,aAAa,iBACZH,OAAA;YAAK2E,KAAK,EAAE;cACVuC,IAAI,EAAE,GAAG;cACTO,SAAS,EAAE,QAAQ;cACnBe,QAAQ,EAAE;YACZ,CAAE;YAAAvD,QAAA,gBACAjF,OAAA;cAAK2E,KAAK,EAAE;gBACV0C,YAAY,EAAE,KAAK;gBACnBxB,QAAQ,EAAE,MAAM;gBAChBb,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5F,OAAA;cACE2E,KAAK,EAAE;gBACLE,OAAO,EAAE,cAAc;gBACvBgD,SAAS,EAAE,SAAS5G,SAAS,eAAeE,SAAS,CAACE,CAAC,GAAGJ,SAAS,OAAOE,SAAS,CAACG,CAAC,GAAGL,SAAS,KAAK;gBACtG6G,eAAe,EAAE,eAAe;gBAChC5B,UAAU,EAAE3E,SAAS,GAAG,MAAM,GAAG,yBAAyB;gBAC1D0E,MAAM,EAAEhF,SAAS,GAAG,CAAC,GAAIM,SAAS,GAAG,UAAU,GAAG,MAAM,GAAI;cAC9D,CAAE;cACFwG,OAAO,EAAEpE,WAAY;cACrBqE,WAAW,EAAE/G,SAAS,GAAG,CAAC,GAAG8C,cAAc,GAAGkE,SAAU;cAAAhD,QAAA,eAExDjF,OAAA;gBACEkI,GAAG,EAAE/H,aAAc;gBACnBkI,GAAG,EAAC,0BAAM;gBACV1D,KAAK,EAAE;kBACL6D,QAAQ,EAAE,MAAM;kBAChBZ,SAAS,EAAE,qBAAqB;kBAChChD,MAAM,EAAE,MAAM;kBACdoB,MAAM,EAAE,mBAAmB;kBAC3BR,YAAY,EAAE,KAAK;kBACnBoD,SAAS,EAAE;gBACb;cAAE;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD5F,OAAA;YAAK2E,KAAK,EAAE;cACVuC,IAAI,EAAE,GAAG;cACTO,SAAS,EAAE,QAAQ;cACnBe,QAAQ,EAAE;YACZ,CAAE;YAAAvD,QAAA,gBACAjF,OAAA;cAAK2E,KAAK,EAAE;gBACV0C,YAAY,EAAE,KAAK;gBACnBxB,QAAQ,EAAE,MAAM;gBAChBb,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAEL,CAACvF,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;cAAK2E,KAAK,EAAE;gBACVlB,KAAK,EAAE,MAAM;gBACbmB,MAAM,EAAE,OAAO;gBACfC,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBY,MAAM,EAAE,iBAAiB;gBACzBR,YAAY,EAAE,KAAK;gBACnBR,KAAK,EAAE,MAAM;gBACbD,eAAe,EAAE;cACnB,CAAE;cAAAE,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN,EAEArF,UAAU,iBACTP,OAAA;cAAK2E,KAAK,EAAE;gBACVlB,KAAK,EAAE,MAAM;gBACbmB,MAAM,EAAE,OAAO;gBACfC,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBY,MAAM,EAAE,mBAAmB;gBAC3BR,YAAY,EAAE,KAAK;gBACnBR,KAAK,EAAE,SAAS;gBAChBD,eAAe,EAAE;cACnB,CAAE;cAAAE,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN,eAED5F,OAAA;cACE2E,KAAK,EAAE;gBACLE,OAAO,EAAExE,WAAW,GAAG,cAAc,GAAG,MAAM;gBAC9CwH,SAAS,EAAE,SAAS5G,SAAS,eAAeE,SAAS,CAACE,CAAC,GAAGJ,SAAS,OAAOE,SAAS,CAACG,CAAC,GAAGL,SAAS,KAAK;gBACtG6G,eAAe,EAAE,eAAe;gBAChC5B,UAAU,EAAE3E,SAAS,GAAG,MAAM,GAAG,yBAAyB;gBAC1D0E,MAAM,EAAEhF,SAAS,GAAG,CAAC,GAAIM,SAAS,GAAG,UAAU,GAAG,MAAM,GAAI;cAC9D,CAAE;cACFwG,OAAO,EAAEpE,WAAY;cACrBqE,WAAW,EAAE/G,SAAS,GAAG,CAAC,GAAG8C,cAAc,GAAGkE,SAAU;cAAAhD,QAAA,eAExDjF,OAAA;gBACEkI,GAAG,EAAE,gCAAgChI,MAAM,CAACmC,YAAY,MAAM8F,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;gBAC3EC,GAAG,EAAC,0BAAM;gBACVC,MAAM,EAAExG,eAAgB;gBACxByG,OAAO,EAAExG,gBAAiB;gBAC1B4C,KAAK,EAAE;kBACL6D,QAAQ,EAAE,MAAM;kBAChBZ,SAAS,EAAE,qBAAqB;kBAChChD,MAAM,EAAE,MAAM;kBACdoB,MAAM,EAAE,mBAAmB;kBAC3BR,YAAY,EAAE,KAAK;kBACnBoD,SAAS,EAAE;gBACb;cAAE;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLvF,WAAW,iBACVL,OAAA;MAAK2E,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdG,eAAe,EAAE,MAAM;QACvBiE,SAAS,EAAE,gBAAgB;QAC3BnE,OAAO,EAAE,MAAM;QACfM,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBG,GAAG,EAAE,MAAM;QACXF,OAAO,EAAE,QAAQ;QACjBC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,gBACAjF,OAAA;QACE+F,OAAO,EAAE/D,aAAc;QACvB2C,KAAK,EAAE;UACLU,OAAO,EAAE,UAAU;UACnBN,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,OAAO;UACdgB,MAAM,EAAE,MAAM;UACdR,YAAY,EAAE,KAAK;UACnBS,MAAM,EAAE,SAAS;UACjBJ,QAAQ,EAAE,MAAM;UAChBhB,OAAO,EAAE,MAAM;UACfM,UAAU,EAAE,QAAQ;UACpBI,GAAG,EAAE,KAAK;UACVW,UAAU,EAAE;QACd,CAAE;QACFO,YAAY,EAAG5D,CAAC,IAAKA,CAAC,CAAC6D,MAAM,CAAC/B,KAAK,CAACI,eAAe,GAAG,SAAU;QAChE4B,YAAY,EAAG9D,CAAC,IAAKA,CAAC,CAAC6D,MAAM,CAAC/B,KAAK,CAACI,eAAe,GAAG,SAAU;QAAAE,QAAA,gBAEhEjF,OAAA;UAAAiF,QAAA,EAAM;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,4BAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET5F,OAAA;QACEoC,IAAI,EAAE,gCAAgClC,MAAM,CAACmC,YAAY,EAAG;QAC5DqE,MAAM,EAAC,QAAQ;QACfuC,GAAG,EAAC,qBAAqB;QACzBtE,KAAK,EAAE;UACLU,OAAO,EAAE,UAAU;UACnBN,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,OAAO;UACdkE,cAAc,EAAE,MAAM;UACtB1D,YAAY,EAAE,KAAK;UACnBK,QAAQ,EAAE,MAAM;UAChBhB,OAAO,EAAE,MAAM;UACfM,UAAU,EAAE,QAAQ;UACpBI,GAAG,EAAE,KAAK;UACVW,UAAU,EAAE;QACd,CAAE;QACFO,YAAY,EAAG5D,CAAC,IAAKA,CAAC,CAAC6D,MAAM,CAAC/B,KAAK,CAACI,eAAe,GAAG,SAAU;QAChE4B,YAAY,EAAG9D,CAAC,IAAKA,CAAC,CAAC6D,MAAM,CAAC/B,KAAK,CAACI,eAAe,GAAG,SAAU;QAAAE,QAAA,gBAEhEjF,OAAA;UAAAiF,QAAA,EAAM;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,kCAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJ5F,OAAA;QAAK2E,KAAK,EAAE;UACVkB,QAAQ,EAAE,MAAM;UAChBb,KAAK,EAAE,MAAM;UACbmE,UAAU,EAAE;QACd,CAAE;QAAAlE,QAAA,GAAC,gBACG,EAAC/E,MAAM,CAACqC,QAAQ;MAAA;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxF,EAAA,CA51BIH,UAAU;AAAAmJ,EAAA,GAAVnJ,UAAU;AA81BhB,eAAeA,UAAU;AAAC,IAAAmJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}