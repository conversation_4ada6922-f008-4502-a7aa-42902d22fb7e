{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResultView = ({\n  result,\n  originalImage\n}) => {\n  _s();\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const [enhancedImageDimensions, setEnhancedImageDimensions] = useState(null);\n  const containerRef = useRef(null);\n  const enhancedImageRef = useRef(null);\n  const handleImageLoad = e => {\n    setImageLoaded(true);\n    setImageError(false);\n\n    // 获取增强图片的实际显示尺寸\n    if (e && e.target) {\n      const img = e.target;\n      setEnhancedImageDimensions({\n        width: img.offsetWidth,\n        height: img.offsetHeight,\n        naturalWidth: img.naturalWidth,\n        naturalHeight: img.naturalHeight\n      });\n    }\n  };\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleMouseDown = e => {\n    setIsDragging(true);\n    e.preventDefault();\n  };\n  const handleMouseMove = e => {\n    if (!isDragging || !containerRef.current) return;\n    const rect = containerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, x / rect.width * 100));\n    setSplitPosition(percentage);\n  };\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#ddd'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '40px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        padding: '0 16px',\n        flexShrink: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '13px',\n            color: '#ddd'\n          },\n          children: \"\\u5904\\u7406\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), result.message && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: [\"\\u2022 \", result.message]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '6px',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('split'),\n          style: {\n            padding: '4px 8px',\n            backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u5206\\u5272\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('side-by-side'),\n          style: {\n            padding: '4px 8px',\n            backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u5E76\\u6392\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), viewMode === 'split' && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: resetSplit,\n          style: {\n            padding: '4px 8px',\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u91CD\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), result.params_used && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: showParams ? 'auto' : '32px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        padding: '8px 16px',\n        flexShrink: 0,\n        transition: 'height 0.3s ease'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: \"\\u5904\\u7406\\u53C2\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this), !showParams && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '12px',\n              fontSize: '12px',\n              color: '#ddd'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [result.params_used.scale, \"x\\u8D85\\u5206\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: result.params_used.use_realesrgan ? 'RealESRGAN' : '插值'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u9510\\u5316\", (result.params_used.sharpening * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u7F8E\\u989C\", (result.params_used.beauty * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowParams(!showParams),\n          style: {\n            padding: '2px 6px',\n            backgroundColor: 'transparent',\n            color: '#aaa',\n            border: '1px solid #555',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '11px',\n            transition: 'all 0.2s'\n          },\n          onMouseEnter: e => {\n            e.target.style.backgroundColor = '#555';\n            e.target.style.color = '#fff';\n          },\n          onMouseLeave: e => {\n            e.target.style.backgroundColor = 'transparent';\n            e.target.style.color = '#aaa';\n          },\n          children: showParams ? '▲' : '▼'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this), showParams && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '12px',\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',\n          gap: '8px',\n          fontSize: '12px',\n          color: '#ddd'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u8D85\\u5206\\u500D\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 20\n          }, this), \" \", result.params_used.scale, \"x\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"AI\\u6A21\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 20\n          }, this), \" \", result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u9510\\u5316:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 20\n          }, this), \" \", (result.params_used.sharpening * 100).toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u964D\\u566A:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 20\n          }, this), \" \", result.params_used.denoising]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u9971\\u548C\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 20\n          }, this), \" \", result.params_used.saturation.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u5BF9\\u6BD4\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 20\n          }, this), \" \", result.params_used.contrast.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u4EAE\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 20\n          }, this), \" \", result.params_used.brightness > 0 ? '+' : '', result.params_used.brightness]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u7F8E\\u989C:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 20\n          }, this), \" \", (result.params_used.beauty * 100).toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column',\n        overflow: 'hidden',\n        position: 'relative'\n      },\n      children: viewMode === 'split' ?\n      /*#__PURE__*/\n      // 分割线对比模式\n      _jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          padding: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '24px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            marginBottom: '12px',\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"\\u5DE6\\u4FA7\\uFF1A\\u539F\\u59CB\\u56FE\\u50CF | \\u53F3\\u4FA7\\uFF1A\\u589E\\u5F3A\\u56FE\\u50CF | \\u5206\\u5272\\u4F4D\\u7F6E\\uFF1A\", splitPosition.toFixed(0), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: containerRef,\n          style: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            cursor: isDragging ? 'ew-resize' : 'default',\n            userSelect: 'none',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative',\n              width: '100%',\n              height: '100%',\n              maxHeight: 'calc(100vh - 200px)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: [imageError ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: '300px',\n                border: '1px dashed #666',\n                borderRadius: '4px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: '#ff6b6b',\n                backgroundColor: '#2b2b2b'\n              },\n              children: \"\\u589E\\u5F3A\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n              ref: enhancedImageRef,\n              src: `http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,\n              alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n              onLoad: handleImageLoad,\n              onError: handleImageError,\n              style: {\n                maxWidth: '100%',\n                maxHeight: '100%',\n                height: 'auto',\n                border: '1px solid #555',\n                borderRadius: '4px',\n                display: 'block',\n                opacity: imageLoaded ? 1 : 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                width: `${splitPosition}%`,\n                height: '100%',\n                overflow: 'hidden',\n                borderRadius: '4px 0 0 4px'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: originalImage,\n                alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n                style: {\n                  width: enhancedImageDimensions ? `${enhancedImageDimensions.width * 100 / splitPosition}px` : `${100 * 100 / splitPosition}%`,\n                  height: enhancedImageDimensions ? `${enhancedImageDimensions.height}px` : '100%',\n                  objectFit: 'cover',\n                  imageRendering: 'pixelated',\n                  // 使用最近邻插值\n                  border: '1px solid #555',\n                  borderRadius: '4px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: 0,\n                left: `${splitPosition}%`,\n                width: '2px',\n                height: '100%',\n                backgroundColor: '#4a90e2',\n                cursor: 'ew-resize',\n                boxShadow: '0 0 4px rgba(74, 144, 226, 0.5)',\n                transform: 'translateX(-1px)',\n                zIndex: 10\n              },\n              onMouseDown: handleMouseDown,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '50%',\n                  left: '50%',\n                  transform: 'translate(-50%, -50%)',\n                  width: '16px',\n                  height: '32px',\n                  backgroundColor: '#4a90e2',\n                  borderRadius: '8px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: 'white',\n                  fontSize: '10px',\n                  fontWeight: 'bold',\n                  boxShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                },\n                children: \"\\u27F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 19\n            }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)',\n                color: '#aaa',\n                backgroundColor: 'rgba(43,43,43,0.9)',\n                padding: '8px 12px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: \"\\u52A0\\u8F7D\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      // 并排对比模式\n      _jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          padding: '16px',\n          gap: '16px',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: [originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: '1',\n            textAlign: 'center',\n            maxWidth: '50%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '8px',\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: \"\\u539F\\u59CB\\u56FE\\u50CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: originalImage,\n            alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n            style: {\n              maxWidth: '100%',\n              maxHeight: 'calc(100vh - 250px)',\n              height: 'auto',\n              border: '1px solid #4a90e2',\n              borderRadius: '4px',\n              objectFit: 'contain'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: '1',\n            textAlign: 'center',\n            maxWidth: '50%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '8px',\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: \"\\u589E\\u5F3A\\u56FE\\u50CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              height: '200px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              border: '1px dashed #666',\n              borderRadius: '4px',\n              color: '#aaa',\n              backgroundColor: '#2b2b2b'\n            },\n            children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u56FE\\u50CF...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 17\n          }, this), imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              height: '200px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              border: '1px solid #ff6b6b',\n              borderRadius: '4px',\n              color: '#ff6b6b',\n              backgroundColor: '#2b2b2b'\n            },\n            children: \"\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,\n            alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n            onLoad: handleImageLoad,\n            onError: handleImageError,\n            style: {\n              maxWidth: '100%',\n              maxHeight: 'calc(100vh - 250px)',\n              height: 'auto',\n              border: '1px solid #28ca42',\n              borderRadius: '4px',\n              objectFit: 'contain',\n              display: imageLoaded ? 'block' : 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '48px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        gap: '12px',\n        padding: '0 16px',\n        flexShrink: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: downloadImage,\n        style: {\n          padding: '6px 12px',\n          backgroundColor: '#28ca42',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer',\n          fontSize: '13px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '6px',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.target.style.backgroundColor = '#22a83a',\n        onMouseLeave: e => e.target.style.backgroundColor = '#28ca42',\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCE5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this), \"\\u4E0B\\u8F7D\\u56FE\\u50CF\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: `http://localhost:8001/result/${result.enhanced_url}`,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        style: {\n          padding: '6px 12px',\n          backgroundColor: '#4a90e2',\n          color: 'white',\n          textDecoration: 'none',\n          borderRadius: '4px',\n          fontSize: '13px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '6px',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.target.style.backgroundColor = '#357abd',\n        onMouseLeave: e => e.target.style.backgroundColor = '#4a90e2',\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDD0D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 13\n        }, this), \"\\u65B0\\u7A97\\u53E3\\u67E5\\u770B\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#aaa',\n          marginLeft: 'auto'\n        },\n        children: [\"\\u6587\\u4EF6: \", result.filename]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 513,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultView, \"pgoBVvoLCrn76VTWTWx7O9HTtLw=\");\n_c = ResultView;\nexport default ResultView;\nvar _c;\n$RefreshReg$(_c, \"ResultView\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "ResultView", "result", "originalImage", "_s", "imageLoaded", "setImageLoaded", "imageError", "setImageError", "showParams", "setShowParams", "splitPosition", "setSplitPosition", "isDragging", "setIsDragging", "viewMode", "setViewMode", "enhancedImageDimensions", "setEnhancedImageDimensions", "containerRef", "enhancedImageRef", "handleImageLoad", "e", "target", "img", "width", "offsetWidth", "height", "offsetHeight", "naturalWidth", "naturalHeight", "handleImageError", "downloadImage", "link", "document", "createElement", "href", "enhanced_url", "download", "filename", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleMouseDown", "preventDefault", "handleMouseMove", "current", "rect", "getBoundingClientRect", "x", "clientX", "left", "percentage", "Math", "max", "min", "handleMouseUp", "addEventListener", "removeEventListener", "resetSplit", "style", "display", "flexDirection", "backgroundColor", "color", "children", "borderBottom", "alignItems", "justifyContent", "padding", "flexShrink", "gap", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "message", "onClick", "border", "cursor", "transition", "params_used", "scale", "use_realesrgan", "sharpening", "toFixed", "beauty", "onMouseEnter", "onMouseLeave", "marginTop", "gridTemplateColumns", "denoising", "saturation", "contrast", "brightness", "flex", "overflow", "position", "marginBottom", "ref", "userSelect", "maxHeight", "src", "Date", "now", "alt", "onLoad", "onError", "max<PERSON><PERSON><PERSON>", "opacity", "top", "objectFit", "imageRendering", "boxShadow", "transform", "zIndex", "onMouseDown", "fontWeight", "textAlign", "borderTop", "rel", "textDecoration", "marginLeft", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\n\nconst ResultView = ({ result, originalImage }) => {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const [enhancedImageDimensions, setEnhancedImageDimensions] = useState(null);\n  const containerRef = useRef(null);\n  const enhancedImageRef = useRef(null);\n\n  const handleImageLoad = (e) => {\n    setImageLoaded(true);\n    setImageError(false);\n\n    // 获取增强图片的实际显示尺寸\n    if (e && e.target) {\n      const img = e.target;\n      setEnhancedImageDimensions({\n        width: img.offsetWidth,\n        height: img.offsetHeight,\n        naturalWidth: img.naturalWidth,\n        naturalHeight: img.naturalHeight\n      });\n    }\n  };\n\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleMouseDown = (e) => {\n    setIsDragging(true);\n    e.preventDefault();\n  };\n\n  const handleMouseMove = (e) => {\n    if (!isDragging || !containerRef.current) return;\n\n    const rect = containerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));\n    setSplitPosition(percentage);\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n\n  return (\n    <div style={{ \n      width: '100%', \n      height: '100%', \n      display: 'flex', \n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#ddd'\n    }}>\n      {/* 顶部工具栏 */}\n      <div style={{\n        height: '40px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        padding: '0 16px',\n        flexShrink: 0\n      }}>\n        {/* 左侧状态指示 */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div style={{\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }}></div>\n          <span style={{ fontSize: '13px', color: '#ddd' }}>处理完成</span>\n          {result.message && (\n            <span style={{ fontSize: '12px', color: '#aaa' }}>• {result.message}</span>\n          )}\n        </div>\n\n        {/* 右侧视图控制 */}\n        <div style={{ display: 'flex', gap: '6px', alignItems: 'center' }}>\n          <button\n            onClick={() => setViewMode('split')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '12px',\n              transition: 'background-color 0.2s'\n            }}\n          >\n            分割对比\n          </button>\n          <button\n            onClick={() => setViewMode('side-by-side')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '12px',\n              transition: 'background-color 0.2s'\n            }}\n          >\n            并排对比\n          </button>\n          {viewMode === 'split' && (\n            <button\n              onClick={resetSplit}\n              style={{\n                padding: '4px 8px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '12px',\n                transition: 'background-color 0.2s'\n              }}\n            >\n              重置\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* 参数信息栏 */}\n      {result.params_used && (\n        <div style={{\n          height: showParams ? 'auto' : '32px',\n          backgroundColor: '#2b2b2b',\n          borderBottom: '1px solid #555',\n          padding: '8px 16px',\n          flexShrink: 0,\n          transition: 'height 0.3s ease'\n        }}>\n          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n              <span style={{ fontSize: '12px', color: '#aaa' }}>处理参数:</span>\n              {!showParams && (\n                <div style={{ display: 'flex', gap: '12px', fontSize: '12px', color: '#ddd' }}>\n                  <span>{result.params_used.scale}x超分</span>\n                  <span>{result.params_used.use_realesrgan ? 'RealESRGAN' : '插值'}</span>\n                  <span>锐化{(result.params_used.sharpening * 100).toFixed(0)}%</span>\n                  <span>美颜{(result.params_used.beauty * 100).toFixed(0)}%</span>\n                </div>\n              )}\n            </div>\n            <button\n              onClick={() => setShowParams(!showParams)}\n              style={{\n                padding: '2px 6px',\n                backgroundColor: 'transparent',\n                color: '#aaa',\n                border: '1px solid #555',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px',\n                transition: 'all 0.2s'\n              }}\n              onMouseEnter={(e) => {\n                e.target.style.backgroundColor = '#555';\n                e.target.style.color = '#fff';\n              }}\n              onMouseLeave={(e) => {\n                e.target.style.backgroundColor = 'transparent';\n                e.target.style.color = '#aaa';\n              }}\n            >\n              {showParams ? '▲' : '▼'}\n            </button>\n          </div>\n\n          {showParams && (\n            <div style={{ \n              marginTop: '12px', \n              display: 'grid', \n              gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', \n              gap: '8px',\n              fontSize: '12px',\n              color: '#ddd'\n            }}>\n              <div><span style={{ color: '#aaa' }}>超分倍数:</span> {result.params_used.scale}x</div>\n              <div><span style={{ color: '#aaa' }}>AI模型:</span> {result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值'}</div>\n              <div><span style={{ color: '#aaa' }}>锐化:</span> {(result.params_used.sharpening * 100).toFixed(0)}%</div>\n              <div><span style={{ color: '#aaa' }}>降噪:</span> {result.params_used.denoising}</div>\n              <div><span style={{ color: '#aaa' }}>饱和度:</span> {result.params_used.saturation.toFixed(1)}</div>\n              <div><span style={{ color: '#aaa' }}>对比度:</span> {result.params_used.contrast.toFixed(1)}</div>\n              <div><span style={{ color: '#aaa' }}>亮度:</span> {result.params_used.brightness > 0 ? '+' : ''}{result.params_used.brightness}</div>\n              <div><span style={{ color: '#aaa' }}>美颜:</span> {(result.params_used.beauty * 100).toFixed(0)}%</div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* 主图像显示区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex', \n        flexDirection: 'column',\n        overflow: 'hidden',\n        position: 'relative'\n      }}>\n        {viewMode === 'split' ? (\n          // 分割线对比模式\n          <div style={{ \n            flex: 1, \n            display: 'flex', \n            flexDirection: 'column',\n            padding: '16px'\n          }}>\n            {/* 分割线信息栏 */}\n            <div style={{\n              height: '24px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              marginBottom: '12px',\n              fontSize: '12px',\n              color: '#aaa'\n            }}>\n              <span>左侧：原始图像 | 右侧：增强图像 | 分割位置：{splitPosition.toFixed(0)}%</span>\n            </div>\n\n            <div\n              ref={containerRef}\n              style={{\n                position: 'relative',\n                width: '100%',\n                height: '100%',\n                cursor: isDragging ? 'ew-resize' : 'default',\n                userSelect: 'none',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}\n            >\n              {originalImage && (\n                <div style={{ \n                  position: 'relative', \n                  width: '100%', \n                  height: '100%',\n                  maxHeight: 'calc(100vh - 200px)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>\n                  {/* 增强图像作为背景 */}\n                  {imageError ? (\n                    <div style={{\n                      width: '100%',\n                      height: '300px',\n                      border: '1px dashed #666',\n                      borderRadius: '4px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: '#ff6b6b',\n                      backgroundColor: '#2b2b2b'\n                    }}>\n                      增强图像加载失败\n                    </div>\n                  ) : (\n                    <img\n                      ref={enhancedImageRef}\n                      src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                      alt=\"增强图像\"\n                      onLoad={handleImageLoad}\n                      onError={handleImageError}\n                      style={{\n                        maxWidth: '100%',\n                        maxHeight: '100%',\n                        height: 'auto',\n                        border: '1px solid #555',\n                        borderRadius: '4px',\n                        display: 'block',\n                        opacity: imageLoaded ? 1 : 0.5\n                      }}\n                    />\n                  )}\n\n                  {/* 原始图像覆盖层 */}\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      width: `${splitPosition}%`,\n                      height: '100%',\n                      overflow: 'hidden',\n                      borderRadius: '4px 0 0 4px'\n                    }}\n                  >\n                    <img\n                      src={originalImage}\n                      alt=\"原始图像\"\n                      style={{\n                        width: enhancedImageDimensions ? `${enhancedImageDimensions.width * 100 / splitPosition}px` : `${100 * 100 / splitPosition}%`,\n                        height: enhancedImageDimensions ? `${enhancedImageDimensions.height}px` : '100%',\n                        objectFit: 'cover',\n                        imageRendering: 'pixelated', // 使用最近邻插值\n                        border: '1px solid #555',\n                        borderRadius: '4px'\n                      }}\n                    />\n                  </div>\n\n                  {/* 分割线 */}\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: `${splitPosition}%`,\n                      width: '2px',\n                      height: '100%',\n                      backgroundColor: '#4a90e2',\n                      cursor: 'ew-resize',\n                      boxShadow: '0 0 4px rgba(74, 144, 226, 0.5)',\n                      transform: 'translateX(-1px)',\n                      zIndex: 10\n                    }}\n                    onMouseDown={handleMouseDown}\n                  >\n                    {/* 分割线手柄 */}\n                    <div\n                      style={{\n                        position: 'absolute',\n                        top: '50%',\n                        left: '50%',\n                        transform: 'translate(-50%, -50%)',\n                        width: '16px',\n                        height: '32px',\n                        backgroundColor: '#4a90e2',\n                        borderRadius: '8px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        color: 'white',\n                        fontSize: '10px',\n                        fontWeight: 'bold',\n                        boxShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                      }}\n                    >\n                      ⟷\n                    </div>\n                  </div>\n\n                  {!imageLoaded && !imageError && (\n                    <div style={{\n                      position: 'absolute',\n                      top: '50%',\n                      left: '50%',\n                      transform: 'translate(-50%, -50%)',\n                      color: '#aaa',\n                      backgroundColor: 'rgba(43,43,43,0.9)',\n                      padding: '8px 12px',\n                      borderRadius: '4px',\n                      fontSize: '12px'\n                    }}>\n                      加载中...\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        ) : (\n          // 并排对比模式\n          <div style={{ \n            flex: 1, \n            display: 'flex', \n            padding: '16px',\n            gap: '16px',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            {/* 原始图像 */}\n            {originalImage && (\n              <div style={{ \n                flex: '1', \n                textAlign: 'center',\n                maxWidth: '50%'\n              }}>\n                <div style={{ \n                  marginBottom: '8px', \n                  fontSize: '12px', \n                  color: '#aaa' \n                }}>\n                  原始图像\n                </div>\n                <img\n                  src={originalImage}\n                  alt=\"原始图像\"\n                  style={{\n                    maxWidth: '100%',\n                    maxHeight: 'calc(100vh - 250px)',\n                    height: 'auto',\n                    border: '1px solid #4a90e2',\n                    borderRadius: '4px',\n                    objectFit: 'contain'\n                  }}\n                />\n              </div>\n            )}\n\n            {/* 增强图像 */}\n            <div style={{ \n              flex: '1', \n              textAlign: 'center',\n              maxWidth: '50%'\n            }}>\n              <div style={{ \n                marginBottom: '8px', \n                fontSize: '12px', \n                color: '#aaa' \n              }}>\n                增强图像\n              </div>\n\n              {!imageLoaded && !imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '200px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px dashed #666',\n                  borderRadius: '4px',\n                  color: '#aaa',\n                  backgroundColor: '#2b2b2b'\n                }}>\n                  正在加载图像...\n                </div>\n              )}\n\n              {imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '200px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px solid #ff6b6b',\n                  borderRadius: '4px',\n                  color: '#ff6b6b',\n                  backgroundColor: '#2b2b2b'\n                }}>\n                  图像加载失败\n                </div>\n              )}\n\n              <img\n                src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                alt=\"增强图像\"\n                onLoad={handleImageLoad}\n                onError={handleImageError}\n                style={{\n                  maxWidth: '100%',\n                  maxHeight: 'calc(100vh - 250px)',\n                  height: 'auto',\n                  border: '1px solid #28ca42',\n                  borderRadius: '4px',\n                  objectFit: 'contain',\n                  display: imageLoaded ? 'block' : 'none'\n                }}\n              />\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 底部控制栏 */}\n      {imageLoaded && (\n        <div style={{\n          height: '48px',\n          backgroundColor: '#333',\n          borderTop: '1px solid #555',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '12px',\n          padding: '0 16px',\n          flexShrink: 0\n        }}>\n          <button\n            onClick={downloadImage}\n            style={{\n              padding: '6px 12px',\n              backgroundColor: '#28ca42',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '13px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px',\n              transition: 'background-color 0.2s'\n            }}\n            onMouseEnter={(e) => e.target.style.backgroundColor = '#22a83a'}\n            onMouseLeave={(e) => e.target.style.backgroundColor = '#28ca42'}\n          >\n            <span>📥</span>\n            下载图像\n          </button>\n\n          <a\n            href={`http://localhost:8001/result/${result.enhanced_url}`}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            style={{\n              padding: '6px 12px',\n              backgroundColor: '#4a90e2',\n              color: 'white',\n              textDecoration: 'none',\n              borderRadius: '4px',\n              fontSize: '13px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px',\n              transition: 'background-color 0.2s'\n            }}\n            onMouseEnter={(e) => e.target.style.backgroundColor = '#357abd'}\n            onMouseLeave={(e) => e.target.style.backgroundColor = '#4a90e2'}\n          >\n            <span>🔍</span>\n            新窗口查看\n          </a>\n\n          <div style={{\n            fontSize: '12px',\n            color: '#aaa',\n            marginLeft: 'auto'\n          }}>\n            文件: {result.filename}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ResultView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,UAAU,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAACqB,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5E,MAAMuB,YAAY,GAAGtB,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMuB,gBAAgB,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAErC,MAAMwB,eAAe,GAAIC,CAAC,IAAK;IAC7BhB,cAAc,CAAC,IAAI,CAAC;IACpBE,aAAa,CAAC,KAAK,CAAC;;IAEpB;IACA,IAAIc,CAAC,IAAIA,CAAC,CAACC,MAAM,EAAE;MACjB,MAAMC,GAAG,GAAGF,CAAC,CAACC,MAAM;MACpBL,0BAA0B,CAAC;QACzBO,KAAK,EAAED,GAAG,CAACE,WAAW;QACtBC,MAAM,EAAEH,GAAG,CAACI,YAAY;QACxBC,YAAY,EAAEL,GAAG,CAACK,YAAY;QAC9BC,aAAa,EAAEN,GAAG,CAACM;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BvB,aAAa,CAAC,IAAI,CAAC;IACnBF,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,gCAAgClC,MAAM,CAACmC,YAAY,EAAE;IACjEJ,IAAI,CAACK,QAAQ,GAAG,YAAYpC,MAAM,CAACqC,QAAQ,EAAE;IAC7CL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;IAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;IACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMW,eAAe,GAAItB,CAAC,IAAK;IAC7BR,aAAa,CAAC,IAAI,CAAC;IACnBQ,CAAC,CAACuB,cAAc,CAAC,CAAC;EACpB,CAAC;EAED,MAAMC,eAAe,GAAIxB,CAAC,IAAK;IAC7B,IAAI,CAACT,UAAU,IAAI,CAACM,YAAY,CAAC4B,OAAO,EAAE;IAE1C,MAAMC,IAAI,GAAG7B,YAAY,CAAC4B,OAAO,CAACE,qBAAqB,CAAC,CAAC;IACzD,MAAMC,CAAC,GAAG5B,CAAC,CAAC6B,OAAO,GAAGH,IAAI,CAACI,IAAI;IAC/B,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAGN,CAAC,GAAGF,IAAI,CAACvB,KAAK,GAAI,GAAG,CAAC,CAAC;IACrEb,gBAAgB,CAACyC,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1B3C,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACAhB,SAAS,CAAC,MAAM;IACd,IAAIe,UAAU,EAAE;MACdqB,QAAQ,CAACwB,gBAAgB,CAAC,WAAW,EAAEZ,eAAe,CAAC;MACvDZ,QAAQ,CAACwB,gBAAgB,CAAC,SAAS,EAAED,aAAa,CAAC;MACnD,OAAO,MAAM;QACXvB,QAAQ,CAACyB,mBAAmB,CAAC,WAAW,EAAEb,eAAe,CAAC;QAC1DZ,QAAQ,CAACyB,mBAAmB,CAAC,SAAS,EAAEF,aAAa,CAAC;MACxD,CAAC;IACH;EACF,CAAC,EAAE,CAAC5C,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAM+C,UAAU,GAAGA,CAAA,KAAM;IACvBhD,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,oBACEZ,OAAA;IAAK6D,KAAK,EAAE;MACVpC,KAAK,EAAE,MAAM;MACbE,MAAM,EAAE,MAAM;MACdmC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,eAAe,EAAE,SAAS;MAC1BC,KAAK,EAAE;IACT,CAAE;IAAAC,QAAA,gBAEAlE,OAAA;MAAK6D,KAAK,EAAE;QACVlC,MAAM,EAAE,MAAM;QACdqC,eAAe,EAAE,SAAS;QAC1BG,YAAY,EAAE,gBAAgB;QAC9BL,OAAO,EAAE,MAAM;QACfM,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BC,OAAO,EAAE,QAAQ;QACjBC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,gBAEAlE,OAAA;QAAK6D,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEM,UAAU,EAAE,QAAQ;UAAEI,GAAG,EAAE;QAAO,CAAE;QAAAN,QAAA,gBACjElE,OAAA;UAAK6D,KAAK,EAAE;YACVpC,KAAK,EAAE,KAAK;YACZE,MAAM,EAAE,KAAK;YACb8C,YAAY,EAAE,KAAK;YACnBT,eAAe,EAAE;UACnB;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACT7E,OAAA;UAAM6D,KAAK,EAAE;YAAEiB,QAAQ,EAAE,MAAM;YAAEb,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,EAAC;QAAI;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC5D3E,MAAM,CAAC6E,OAAO,iBACb/E,OAAA;UAAM6D,KAAK,EAAE;YAAEiB,QAAQ,EAAE,MAAM;YAAEb,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,GAAC,SAAE,EAAChE,MAAM,CAAC6E,OAAO;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC3E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN7E,OAAA;QAAK6D,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEU,GAAG,EAAE,KAAK;UAAEJ,UAAU,EAAE;QAAS,CAAE;QAAAF,QAAA,gBAChElE,OAAA;UACEgF,OAAO,EAAEA,CAAA,KAAMhE,WAAW,CAAC,OAAO,CAAE;UACpC6C,KAAK,EAAE;YACLS,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAEjD,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,MAAM;YAC1DkD,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7E,OAAA;UACEgF,OAAO,EAAEA,CAAA,KAAMhE,WAAW,CAAC,cAAc,CAAE;UAC3C6C,KAAK,EAAE;YACLS,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAEjD,QAAQ,KAAK,cAAc,GAAG,SAAS,GAAG,MAAM;YACjEkD,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACR9D,QAAQ,KAAK,OAAO,iBACnBf,OAAA;UACEgF,OAAO,EAAEpB,UAAW;UACpBC,KAAK,EAAE;YACLS,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL3E,MAAM,CAACkF,WAAW,iBACjBpF,OAAA;MAAK6D,KAAK,EAAE;QACVlC,MAAM,EAAElB,UAAU,GAAG,MAAM,GAAG,MAAM;QACpCuD,eAAe,EAAE,SAAS;QAC1BG,YAAY,EAAE,gBAAgB;QAC9BG,OAAO,EAAE,UAAU;QACnBC,UAAU,EAAE,CAAC;QACbY,UAAU,EAAE;MACd,CAAE;MAAAjB,QAAA,gBACAlE,OAAA;QAAK6D,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEM,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAH,QAAA,gBACrFlE,OAAA;UAAK6D,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEM,UAAU,EAAE,QAAQ;YAAEI,GAAG,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACjElE,OAAA;YAAM6D,KAAK,EAAE;cAAEiB,QAAQ,EAAE,MAAM;cAAEb,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC7D,CAACpE,UAAU,iBACVT,OAAA;YAAK6D,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEU,GAAG,EAAE,MAAM;cAAEM,QAAQ,EAAE,MAAM;cAAEb,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,gBAC5ElE,OAAA;cAAAkE,QAAA,GAAOhE,MAAM,CAACkF,WAAW,CAACC,KAAK,EAAC,eAAG;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1C7E,OAAA;cAAAkE,QAAA,EAAOhE,MAAM,CAACkF,WAAW,CAACE,cAAc,GAAG,YAAY,GAAG;YAAI;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtE7E,OAAA;cAAAkE,QAAA,GAAM,cAAE,EAAC,CAAChE,MAAM,CAACkF,WAAW,CAACG,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClE7E,OAAA;cAAAkE,QAAA,GAAM,cAAE,EAAC,CAAChE,MAAM,CAACkF,WAAW,CAACK,MAAM,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN7E,OAAA;UACEgF,OAAO,EAAEA,CAAA,KAAMtE,aAAa,CAAC,CAACD,UAAU,CAAE;UAC1CoD,KAAK,EAAE;YACLS,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAE,aAAa;YAC9BC,KAAK,EAAE,MAAM;YACbgB,MAAM,EAAE,gBAAgB;YACxBR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UACFO,YAAY,EAAGpE,CAAC,IAAK;YACnBA,CAAC,CAACC,MAAM,CAACsC,KAAK,CAACG,eAAe,GAAG,MAAM;YACvC1C,CAAC,CAACC,MAAM,CAACsC,KAAK,CAACI,KAAK,GAAG,MAAM;UAC/B,CAAE;UACF0B,YAAY,EAAGrE,CAAC,IAAK;YACnBA,CAAC,CAACC,MAAM,CAACsC,KAAK,CAACG,eAAe,GAAG,aAAa;YAC9C1C,CAAC,CAACC,MAAM,CAACsC,KAAK,CAACI,KAAK,GAAG,MAAM;UAC/B,CAAE;UAAAC,QAAA,EAEDzD,UAAU,GAAG,GAAG,GAAG;QAAG;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELpE,UAAU,iBACTT,OAAA;QAAK6D,KAAK,EAAE;UACV+B,SAAS,EAAE,MAAM;UACjB9B,OAAO,EAAE,MAAM;UACf+B,mBAAmB,EAAE,sCAAsC;UAC3DrB,GAAG,EAAE,KAAK;UACVM,QAAQ,EAAE,MAAM;UAChBb,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,gBACAlE,OAAA;UAAAkE,QAAA,gBAAKlE,OAAA;YAAM6D,KAAK,EAAE;cAAEI,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC3E,MAAM,CAACkF,WAAW,CAACC,KAAK,EAAC,GAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnF7E,OAAA;UAAAkE,QAAA,gBAAKlE,OAAA;YAAM6D,KAAK,EAAE;cAAEI,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC3E,MAAM,CAACkF,WAAW,CAACE,cAAc,GAAG,YAAY,GAAG,MAAM;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnH7E,OAAA;UAAAkE,QAAA,gBAAKlE,OAAA;YAAM6D,KAAK,EAAE;cAAEI,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC,CAAC3E,MAAM,CAACkF,WAAW,CAACG,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzG7E,OAAA;UAAAkE,QAAA,gBAAKlE,OAAA;YAAM6D,KAAK,EAAE;cAAEI,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC3E,MAAM,CAACkF,WAAW,CAACU,SAAS;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpF7E,OAAA;UAAAkE,QAAA,gBAAKlE,OAAA;YAAM6D,KAAK,EAAE;cAAEI,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC3E,MAAM,CAACkF,WAAW,CAACW,UAAU,CAACP,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjG7E,OAAA;UAAAkE,QAAA,gBAAKlE,OAAA;YAAM6D,KAAK,EAAE;cAAEI,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC3E,MAAM,CAACkF,WAAW,CAACY,QAAQ,CAACR,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/F7E,OAAA;UAAAkE,QAAA,gBAAKlE,OAAA;YAAM6D,KAAK,EAAE;cAAEI,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC3E,MAAM,CAACkF,WAAW,CAACa,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE/F,MAAM,CAACkF,WAAW,CAACa,UAAU;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnI7E,OAAA;UAAAkE,QAAA,gBAAKlE,OAAA;YAAM6D,KAAK,EAAE;cAAEI,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC,CAAC3E,MAAM,CAACkF,WAAW,CAACK,MAAM,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClG,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGD7E,OAAA;MAAK6D,KAAK,EAAE;QACVqC,IAAI,EAAE,CAAC;QACPpC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBoC,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE;MACZ,CAAE;MAAAlC,QAAA,EACCnD,QAAQ,KAAK,OAAO;MAAA;MACnB;MACAf,OAAA;QAAK6D,KAAK,EAAE;UACVqC,IAAI,EAAE,CAAC;UACPpC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBO,OAAO,EAAE;QACX,CAAE;QAAAJ,QAAA,gBAEAlE,OAAA;UAAK6D,KAAK,EAAE;YACVlC,MAAM,EAAE,MAAM;YACdmC,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBgC,YAAY,EAAE,MAAM;YACpBvB,QAAQ,EAAE,MAAM;YAChBb,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,eACAlE,OAAA;YAAAkE,QAAA,GAAM,0HAAyB,EAACvD,aAAa,CAAC6E,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eAEN7E,OAAA;UACEsG,GAAG,EAAEnF,YAAa;UAClB0C,KAAK,EAAE;YACLuC,QAAQ,EAAE,UAAU;YACpB3E,KAAK,EAAE,MAAM;YACbE,MAAM,EAAE,MAAM;YACduD,MAAM,EAAErE,UAAU,GAAG,WAAW,GAAG,SAAS;YAC5C0F,UAAU,EAAE,MAAM;YAClBzC,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAH,QAAA,EAED/D,aAAa,iBACZH,OAAA;YAAK6D,KAAK,EAAE;cACVuC,QAAQ,EAAE,UAAU;cACpB3E,KAAK,EAAE,MAAM;cACbE,MAAM,EAAE,MAAM;cACd6E,SAAS,EAAE,qBAAqB;cAChC1C,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAH,QAAA,GAEC3D,UAAU,gBACTP,OAAA;cAAK6D,KAAK,EAAE;gBACVpC,KAAK,EAAE,MAAM;gBACbE,MAAM,EAAE,OAAO;gBACfsD,MAAM,EAAE,iBAAiB;gBACzBR,YAAY,EAAE,KAAK;gBACnBX,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBJ,KAAK,EAAE,SAAS;gBAChBD,eAAe,EAAE;cACnB,CAAE;cAAAE,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAEN7E,OAAA;cACEsG,GAAG,EAAElF,gBAAiB;cACtBqF,GAAG,EAAE,gCAAgCvG,MAAM,CAACmC,YAAY,MAAMqE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;cAC3EC,GAAG,EAAC,0BAAM;cACVC,MAAM,EAAExF,eAAgB;cACxByF,OAAO,EAAE/E,gBAAiB;cAC1B8B,KAAK,EAAE;gBACLkD,QAAQ,EAAE,MAAM;gBAChBP,SAAS,EAAE,MAAM;gBACjB7E,MAAM,EAAE,MAAM;gBACdsD,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBX,OAAO,EAAE,OAAO;gBAChBkD,OAAO,EAAE3G,WAAW,GAAG,CAAC,GAAG;cAC7B;YAAE;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACF,eAGD7E,OAAA;cACE6D,KAAK,EAAE;gBACLuC,QAAQ,EAAE,UAAU;gBACpBa,GAAG,EAAE,CAAC;gBACN7D,IAAI,EAAE,CAAC;gBACP3B,KAAK,EAAE,GAAGd,aAAa,GAAG;gBAC1BgB,MAAM,EAAE,MAAM;gBACdwE,QAAQ,EAAE,QAAQ;gBAClB1B,YAAY,EAAE;cAChB,CAAE;cAAAP,QAAA,eAEFlE,OAAA;gBACEyG,GAAG,EAAEtG,aAAc;gBACnByG,GAAG,EAAC,0BAAM;gBACV/C,KAAK,EAAE;kBACLpC,KAAK,EAAER,uBAAuB,GAAG,GAAGA,uBAAuB,CAACQ,KAAK,GAAG,GAAG,GAAGd,aAAa,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGA,aAAa,GAAG;kBAC7HgB,MAAM,EAAEV,uBAAuB,GAAG,GAAGA,uBAAuB,CAACU,MAAM,IAAI,GAAG,MAAM;kBAChFuF,SAAS,EAAE,OAAO;kBAClBC,cAAc,EAAE,WAAW;kBAAE;kBAC7BlC,MAAM,EAAE,gBAAgB;kBACxBR,YAAY,EAAE;gBAChB;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN7E,OAAA;cACE6D,KAAK,EAAE;gBACLuC,QAAQ,EAAE,UAAU;gBACpBa,GAAG,EAAE,CAAC;gBACN7D,IAAI,EAAE,GAAGzC,aAAa,GAAG;gBACzBc,KAAK,EAAE,KAAK;gBACZE,MAAM,EAAE,MAAM;gBACdqC,eAAe,EAAE,SAAS;gBAC1BkB,MAAM,EAAE,WAAW;gBACnBkC,SAAS,EAAE,iCAAiC;gBAC5CC,SAAS,EAAE,kBAAkB;gBAC7BC,MAAM,EAAE;cACV,CAAE;cACFC,WAAW,EAAE3E,eAAgB;cAAAsB,QAAA,eAG7BlE,OAAA;gBACE6D,KAAK,EAAE;kBACLuC,QAAQ,EAAE,UAAU;kBACpBa,GAAG,EAAE,KAAK;kBACV7D,IAAI,EAAE,KAAK;kBACXiE,SAAS,EAAE,uBAAuB;kBAClC5F,KAAK,EAAE,MAAM;kBACbE,MAAM,EAAE,MAAM;kBACdqC,eAAe,EAAE,SAAS;kBAC1BS,YAAY,EAAE,KAAK;kBACnBX,OAAO,EAAE,MAAM;kBACfM,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE,QAAQ;kBACxBJ,KAAK,EAAE,OAAO;kBACda,QAAQ,EAAE,MAAM;kBAChB0C,UAAU,EAAE,MAAM;kBAClBJ,SAAS,EAAE;gBACb,CAAE;gBAAAlD,QAAA,EACH;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL,CAACxE,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;cAAK6D,KAAK,EAAE;gBACVuC,QAAQ,EAAE,UAAU;gBACpBa,GAAG,EAAE,KAAK;gBACV7D,IAAI,EAAE,KAAK;gBACXiE,SAAS,EAAE,uBAAuB;gBAClCpD,KAAK,EAAE,MAAM;gBACbD,eAAe,EAAE,oBAAoB;gBACrCM,OAAO,EAAE,UAAU;gBACnBG,YAAY,EAAE,KAAK;gBACnBK,QAAQ,EAAE;cACZ,CAAE;cAAAZ,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;MAAA;MAEN;MACA7E,OAAA;QAAK6D,KAAK,EAAE;UACVqC,IAAI,EAAE,CAAC;UACPpC,OAAO,EAAE,MAAM;UACfQ,OAAO,EAAE,MAAM;UACfE,GAAG,EAAE,MAAM;UACXJ,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAH,QAAA,GAEC/D,aAAa,iBACZH,OAAA;UAAK6D,KAAK,EAAE;YACVqC,IAAI,EAAE,GAAG;YACTuB,SAAS,EAAE,QAAQ;YACnBV,QAAQ,EAAE;UACZ,CAAE;UAAA7C,QAAA,gBACAlE,OAAA;YAAK6D,KAAK,EAAE;cACVwC,YAAY,EAAE,KAAK;cACnBvB,QAAQ,EAAE,MAAM;cAChBb,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN7E,OAAA;YACEyG,GAAG,EAAEtG,aAAc;YACnByG,GAAG,EAAC,0BAAM;YACV/C,KAAK,EAAE;cACLkD,QAAQ,EAAE,MAAM;cAChBP,SAAS,EAAE,qBAAqB;cAChC7E,MAAM,EAAE,MAAM;cACdsD,MAAM,EAAE,mBAAmB;cAC3BR,YAAY,EAAE,KAAK;cACnByC,SAAS,EAAE;YACb;UAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGD7E,OAAA;UAAK6D,KAAK,EAAE;YACVqC,IAAI,EAAE,GAAG;YACTuB,SAAS,EAAE,QAAQ;YACnBV,QAAQ,EAAE;UACZ,CAAE;UAAA7C,QAAA,gBACAlE,OAAA;YAAK6D,KAAK,EAAE;cACVwC,YAAY,EAAE,KAAK;cACnBvB,QAAQ,EAAE,MAAM;cAChBb,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAEL,CAACxE,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;YAAK6D,KAAK,EAAE;cACVpC,KAAK,EAAE,MAAM;cACbE,MAAM,EAAE,OAAO;cACfmC,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBY,MAAM,EAAE,iBAAiB;cACzBR,YAAY,EAAE,KAAK;cACnBR,KAAK,EAAE,MAAM;cACbD,eAAe,EAAE;YACnB,CAAE;YAAAE,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,EAEAtE,UAAU,iBACTP,OAAA;YAAK6D,KAAK,EAAE;cACVpC,KAAK,EAAE,MAAM;cACbE,MAAM,EAAE,OAAO;cACfmC,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBY,MAAM,EAAE,mBAAmB;cAC3BR,YAAY,EAAE,KAAK;cACnBR,KAAK,EAAE,SAAS;cAChBD,eAAe,EAAE;YACnB,CAAE;YAAAE,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,eAED7E,OAAA;YACEyG,GAAG,EAAE,gCAAgCvG,MAAM,CAACmC,YAAY,MAAMqE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;YAC3EC,GAAG,EAAC,0BAAM;YACVC,MAAM,EAAExF,eAAgB;YACxByF,OAAO,EAAE/E,gBAAiB;YAC1B8B,KAAK,EAAE;cACLkD,QAAQ,EAAE,MAAM;cAChBP,SAAS,EAAE,qBAAqB;cAChC7E,MAAM,EAAE,MAAM;cACdsD,MAAM,EAAE,mBAAmB;cAC3BR,YAAY,EAAE,KAAK;cACnByC,SAAS,EAAE,SAAS;cACpBpD,OAAO,EAAEzD,WAAW,GAAG,OAAO,GAAG;YACnC;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLxE,WAAW,iBACVL,OAAA;MAAK6D,KAAK,EAAE;QACVlC,MAAM,EAAE,MAAM;QACdqC,eAAe,EAAE,MAAM;QACvB0D,SAAS,EAAE,gBAAgB;QAC3B5D,OAAO,EAAE,MAAM;QACfM,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBG,GAAG,EAAE,MAAM;QACXF,OAAO,EAAE,QAAQ;QACjBC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,gBACAlE,OAAA;QACEgF,OAAO,EAAEhD,aAAc;QACvB6B,KAAK,EAAE;UACLS,OAAO,EAAE,UAAU;UACnBN,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,OAAO;UACdgB,MAAM,EAAE,MAAM;UACdR,YAAY,EAAE,KAAK;UACnBS,MAAM,EAAE,SAAS;UACjBJ,QAAQ,EAAE,MAAM;UAChBhB,OAAO,EAAE,MAAM;UACfM,UAAU,EAAE,QAAQ;UACpBI,GAAG,EAAE,KAAK;UACVW,UAAU,EAAE;QACd,CAAE;QACFO,YAAY,EAAGpE,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACsC,KAAK,CAACG,eAAe,GAAG,SAAU;QAChE2B,YAAY,EAAGrE,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACsC,KAAK,CAACG,eAAe,GAAG,SAAU;QAAAE,QAAA,gBAEhElE,OAAA;UAAAkE,QAAA,EAAM;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,4BAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET7E,OAAA;QACEoC,IAAI,EAAE,gCAAgClC,MAAM,CAACmC,YAAY,EAAG;QAC5Dd,MAAM,EAAC,QAAQ;QACfoG,GAAG,EAAC,qBAAqB;QACzB9D,KAAK,EAAE;UACLS,OAAO,EAAE,UAAU;UACnBN,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,OAAO;UACd2D,cAAc,EAAE,MAAM;UACtBnD,YAAY,EAAE,KAAK;UACnBK,QAAQ,EAAE,MAAM;UAChBhB,OAAO,EAAE,MAAM;UACfM,UAAU,EAAE,QAAQ;UACpBI,GAAG,EAAE,KAAK;UACVW,UAAU,EAAE;QACd,CAAE;QACFO,YAAY,EAAGpE,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACsC,KAAK,CAACG,eAAe,GAAG,SAAU;QAChE2B,YAAY,EAAGrE,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACsC,KAAK,CAACG,eAAe,GAAG,SAAU;QAAAE,QAAA,gBAEhElE,OAAA;UAAAkE,QAAA,EAAM;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,kCAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJ7E,OAAA;QAAK6D,KAAK,EAAE;UACViB,QAAQ,EAAE,MAAM;UAChBb,KAAK,EAAE,MAAM;UACb4D,UAAU,EAAE;QACd,CAAE;QAAA3D,QAAA,GAAC,gBACG,EAAChE,MAAM,CAACqC,QAAQ;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzE,EAAA,CAjkBIH,UAAU;AAAA6H,EAAA,GAAV7H,UAAU;AAmkBhB,eAAeA,UAAU;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}