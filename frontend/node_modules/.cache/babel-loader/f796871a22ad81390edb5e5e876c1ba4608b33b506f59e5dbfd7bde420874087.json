{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const uploadFormRef = useRef(null);\n  const handleUpload = async formData => {\n    setIsLoading(true);\n    setError(null);\n\n    // 保存原始图像用于对比\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => setOriginalImage(e.target.result);\n      reader.readAsDataURL(file);\n    }\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n  };\n  const handleApply = () => {\n    if (uploadFormRef.current && uploadFormRef.current.triggerSubmit) {\n      uploadFormRef.current.triggerSubmit();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '28px',\n        backgroundColor: '#3c3c3c',\n        display: 'flex',\n        alignItems: 'center',\n        paddingLeft: '12px',\n        borderBottom: '1px solid #555'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ff5f57'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ffbd2e'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          textAlign: 'center',\n          color: '#fff',\n          fontSize: '13px',\n          fontWeight: '500'\n        },\n        children: \"\\u56FE\\u50CF\\u589E\\u5F3A\\u5DE5\\u5177\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '32px',\n        backgroundColor: '#3c3c3c',\n        display: 'flex',\n        alignItems: 'center',\n        paddingLeft: '16px',\n        borderBottom: '1px solid #555'\n      },\n      children: ['打开', '保存', '编辑', '调整', '效果', '帮助', '实验室功能', '窗口大小', '图像增强', '自动增强'].map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#ddd',\n          fontSize: '13px',\n          padding: '6px 12px',\n          cursor: 'pointer',\n          borderRadius: '4px',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.target.style.backgroundColor = '#4a4a4a',\n        onMouseLeave: e => e.target.style.backgroundColor = 'transparent',\n        children: item\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          },\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '20px',\n              left: '20px',\n              right: '20px',\n              backgroundColor: '#d32f2f',\n              color: 'white',\n              padding: '12px',\n              borderRadius: '4px',\n              fontSize: '14px',\n              zIndex: 10\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u9519\\u8BEF:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this), \" \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleReset,\n              style: {\n                marginLeft: '10px',\n                padding: '4px 8px',\n                backgroundColor: 'rgba(255,255,255,0.2)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              },\n              children: \"\\u91CD\\u8BD5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              height: '100%',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              backgroundColor: '#1e1e1e',\n              color: '#ddd',\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '10px',\n                fontSize: '14px'\n              },\n              children: \"\\u6B63\\u5728\\u5904\\u7406\\u56FE\\u50CF\\uFF0C\\u8BF7\\u7A0D\\u5019...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '40px',\n                height: '4px',\n                backgroundColor: '#333',\n                borderRadius: '2px',\n                overflow: 'hidden',\n                margin: '0 auto'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '100%',\n                  height: '100%',\n                  backgroundColor: '#4a90e2',\n                  animation: 'loading 1.5s infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), !result && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              color: '#888',\n              fontSize: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '20px'\n              },\n              children: \"\\uD83D\\uDCC1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"\\u62D6\\u62FD\\u56FE\\u50CF\\u6587\\u4EF6\\u5230\\u6B64\\u5904\\u6216\\u4F7F\\u7528\\u53F3\\u4FA7\\u9762\\u677F\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this), result && /*#__PURE__*/_jsxDEV(ResultView, {\n            result: result,\n            originalImage: originalImage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 24\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '24px',\n            backgroundColor: '#333',\n            borderTop: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            paddingLeft: '12px',\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: \"\\u5C31\\u7EEA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '280px',\n          backgroundColor: '#2b2b2b',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '40px',\n            backgroundColor: '#3c3c3c',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: '#fff',\n            fontSize: '14px',\n            fontWeight: '500'\n          },\n          children: \"\\u8C03\\u6574\\u9762\\u677F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          },\n          children: /*#__PURE__*/_jsxDEV(UploadForm, {\n            onUpload: handleUpload,\n            isLoading: isLoading,\n            ref: uploadFormRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '60px',\n            backgroundColor: '#333',\n            borderTop: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            gap: '10px',\n            padding: '0 15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReset,\n            style: {\n              flex: 1,\n              padding: '8px',\n              backgroundColor: '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '13px'\n            },\n            children: \"\\u91CD\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleApply,\n            disabled: isLoading,\n            style: {\n              flex: 1,\n              padding: '8px',\n              backgroundColor: isLoading ? '#666' : '#4a90e2',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              fontSize: '13px'\n            },\n            children: isLoading ? '处理中...' : '应用'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes loading {\n          0% { transform: translateX(-100%); }\n          100% { transform: translateX(100%); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"AoUWqesfEKvLS+60WoOWtYzxPKw=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "UploadForm", "ResultView", "jsxDEV", "_jsxDEV", "App", "_s", "result", "setResult", "isLoading", "setIsLoading", "error", "setError", "originalImage", "setOriginalImage", "uploadFormRef", "handleUpload", "formData", "file", "get", "reader", "FileReader", "onload", "e", "target", "readAsDataURL", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "data", "err", "console", "message", "handleReset", "handleApply", "current", "triggerSubmit", "style", "height", "backgroundColor", "fontFamily", "display", "flexDirection", "overflow", "children", "alignItems", "paddingLeft", "borderBottom", "gap", "width", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "textAlign", "color", "fontSize", "fontWeight", "map", "item", "index", "padding", "cursor", "transition", "onMouseEnter", "onMouseLeave", "position", "justifyContent", "top", "left", "right", "zIndex", "onClick", "marginLeft", "border", "marginBottom", "margin", "animation", "borderTop", "borderLeft", "onUpload", "ref", "disabled", "jsx", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\n\nfunction App() {\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const uploadFormRef = useRef(null);\n\n  const handleUpload = async (formData) => {\n    setIsLoading(true);\n    setError(null);\n    \n    // 保存原始图像用于对比\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => setOriginalImage(e.target.result);\n      reader.readAsDataURL(file);\n    }\n\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n  };\n\n  const handleApply = () => {\n    if (uploadFormRef.current && uploadFormRef.current.triggerSubmit) {\n      uploadFormRef.current.triggerSubmit();\n    }\n  };\n\n  return (\n    <div style={{ \n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    }}>\n      {/* macOS风格标题栏 */}\n      <div style={{\n        height: '28px',\n        backgroundColor: '#3c3c3c',\n        display: 'flex',\n        alignItems: 'center',\n        paddingLeft: '12px',\n        borderBottom: '1px solid #555'\n      }}>\n        <div style={{ display: 'flex', gap: '8px' }}>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>\n        </div>\n        <div style={{ \n          flex: 1, \n          textAlign: 'center', \n          color: '#fff', \n          fontSize: '13px',\n          fontWeight: '500'\n        }}>\n          图像增强工具\n        </div>\n      </div>\n\n      {/* 菜单栏 */}\n      <div style={{\n        height: '32px',\n        backgroundColor: '#3c3c3c',\n        display: 'flex',\n        alignItems: 'center',\n        paddingLeft: '16px',\n        borderBottom: '1px solid #555'\n      }}>\n        {['打开', '保存', '编辑', '调整', '效果', '帮助', '实验室功能', '窗口大小', '图像增强', '自动增强'].map((item, index) => (\n          <div key={index} style={{\n            color: '#ddd',\n            fontSize: '13px',\n            padding: '6px 12px',\n            cursor: 'pointer',\n            borderRadius: '4px',\n            transition: 'background-color 0.2s'\n          }}\n          onMouseEnter={(e) => e.target.style.backgroundColor = '#4a4a4a'}\n          onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}\n          >\n            {item}\n          </div>\n        ))}\n      </div>\n\n      {/* 主内容区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex',\n        overflow: 'hidden'\n      }}>\n        {/* 左侧主工作区 */}\n        <div style={{\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        }}>\n          {/* 图像显示区域 */}\n          <div style={{\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          }}>\n            {error && (\n              <div style={{ \n                position: 'absolute',\n                top: '20px',\n                left: '20px',\n                right: '20px',\n                backgroundColor: '#d32f2f', \n                color: 'white', \n                padding: '12px', \n                borderRadius: '4px',\n                fontSize: '14px',\n                zIndex: 10\n              }}>\n                <strong>错误:</strong> {error}\n                <button \n                  onClick={handleReset}\n                  style={{\n                    marginLeft: '10px',\n                    padding: '4px 8px',\n                    backgroundColor: 'rgba(255,255,255,0.2)',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '3px',\n                    cursor: 'pointer',\n                    fontSize: '12px'\n                  }}\n                >\n                  重试\n                </button>\n              </div>\n            )}\n\n            {isLoading && (\n              <div style={{\n                width: '100%',\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: '#1e1e1e',\n                color: '#ddd',\n                position: 'relative'\n              }}>\n                <div style={{ marginBottom: '10px', fontSize: '14px' }}>\n                  正在处理图像，请稍候...\n                </div>\n                <div style={{ \n                  width: '40px', \n                  height: '4px', \n                  backgroundColor: '#333',\n                  borderRadius: '2px',\n                  overflow: 'hidden',\n                  margin: '0 auto'\n                }}>\n                  <div style={{\n                    width: '100%',\n                    height: '100%',\n                    backgroundColor: '#4a90e2',\n                    animation: 'loading 1.5s infinite'\n                  }}></div>\n                </div>\n              </div>\n            )}\n\n            {!result && !isLoading && (\n              <div style={{\n                textAlign: 'center',\n                color: '#888',\n                fontSize: '16px'\n              }}>\n                <div style={{ fontSize: '48px', marginBottom: '20px' }}>📁</div>\n                <div>拖拽图像文件到此处或使用右侧面板上传</div>\n              </div>\n            )}\n\n            {result && <ResultView result={result} originalImage={originalImage} />}\n          </div>\n\n          {/* 底部状态栏 */}\n          <div style={{\n            height: '24px',\n            backgroundColor: '#333',\n            borderTop: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            paddingLeft: '12px',\n            fontSize: '12px',\n            color: '#aaa'\n          }}>\n            就绪\n          </div>\n        </div>\n\n        {/* 右侧参数面板 */}\n        <div style={{\n          width: '280px',\n          backgroundColor: '#2b2b2b',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        }}>\n          {/* 面板标题 */}\n          <div style={{\n            height: '40px',\n            backgroundColor: '#3c3c3c',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: '#fff',\n            fontSize: '14px',\n            fontWeight: '500'\n          }}>\n            调整面板\n          </div>\n\n          {/* 参数控制区域 */}\n          <div style={{\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          }}>\n            <UploadForm\n              onUpload={handleUpload}\n              isLoading={isLoading}\n              ref={uploadFormRef}\n            />\n          </div>\n\n          {/* 底部按钮区域 */}\n          <div style={{\n            height: '60px',\n            backgroundColor: '#333',\n            borderTop: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            gap: '10px',\n            padding: '0 15px'\n          }}>\n            <button\n              onClick={handleReset}\n              style={{\n                flex: 1,\n                padding: '8px',\n                backgroundColor: '#555',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '13px'\n              }}\n            >\n              重置\n            </button>\n            <button\n              onClick={handleApply}\n              disabled={isLoading}\n              style={{\n                flex: 1,\n                padding: '8px',\n                backgroundColor: isLoading ? '#666' : '#4a90e2',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: isLoading ? 'not-allowed' : 'pointer',\n                fontSize: '13px'\n              }}\n            >\n              {isLoading ? '处理中...' : '应用'}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        @keyframes loading {\n          0% { transform: translateX(-100%); }\n          100% { transform: translateX(100%); }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAMgB,aAAa,GAAGf,MAAM,CAAC,IAAI,CAAC;EAElC,MAAMgB,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvCP,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMM,IAAI,GAAGD,QAAQ,CAACE,GAAG,CAAC,MAAM,CAAC;IACjC,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKT,gBAAgB,CAACS,CAAC,CAACC,MAAM,CAACjB,MAAM,CAAC;MACxDa,MAAM,CAACK,aAAa,CAACP,IAAI,CAAC;IAC5B;IAEA,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,EAAE;QAC7DC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEZ;MACR,CAAC,CAAC;MAEF,IAAI,CAACS,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,MAAM,CAAC;MAC7C;MAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClCxB,SAAS,CAAC2B,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAAC1B,KAAK,CAAC,OAAO,EAAEyB,GAAG,CAAC;MAC3BxB,QAAQ,CAACwB,GAAG,CAACE,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACR5B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM6B,WAAW,GAAGA,CAAA,KAAM;IACxB/B,SAAS,CAAC,IAAI,CAAC;IACfI,QAAQ,CAAC,IAAI,CAAC;IACdE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM0B,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIzB,aAAa,CAAC0B,OAAO,IAAI1B,aAAa,CAAC0B,OAAO,CAACC,aAAa,EAAE;MAChE3B,aAAa,CAAC0B,OAAO,CAACC,aAAa,CAAC,CAAC;IACvC;EACF,CAAC;EAED,oBACEtC,OAAA;IAAKuC,KAAK,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,eAAe,EAAE,SAAS;MAC1BC,UAAU,EAAE,mEAAmE;MAC/EC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEA9C,OAAA;MAAKuC,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdC,eAAe,EAAE,SAAS;QAC1BE,OAAO,EAAE,MAAM;QACfI,UAAU,EAAE,QAAQ;QACpBC,WAAW,EAAE,MAAM;QACnBC,YAAY,EAAE;MAChB,CAAE;MAAAH,QAAA,gBACA9C,OAAA;QAAKuC,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEO,GAAG,EAAE;QAAM,CAAE;QAAAJ,QAAA,gBAC1C9C,OAAA;UAAKuC,KAAK,EAAE;YAAEY,KAAK,EAAE,MAAM;YAAEX,MAAM,EAAE,MAAM;YAAEY,YAAY,EAAE,KAAK;YAAEX,eAAe,EAAE;UAAU;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGxD,OAAA;UAAKuC,KAAK,EAAE;YAAEY,KAAK,EAAE,MAAM;YAAEX,MAAM,EAAE,MAAM;YAAEY,YAAY,EAAE,KAAK;YAAEX,eAAe,EAAE;UAAU;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGxD,OAAA;UAAKuC,KAAK,EAAE;YAAEY,KAAK,EAAE,MAAM;YAAEX,MAAM,EAAE,MAAM;YAAEY,YAAY,EAAE,KAAK;YAAEX,eAAe,EAAE;UAAU;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC,eACNxD,OAAA;QAAKuC,KAAK,EAAE;UACVkB,IAAI,EAAE,CAAC;UACPC,SAAS,EAAE,QAAQ;UACnBC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE;QACd,CAAE;QAAAf,QAAA,EAAC;MAEH;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxD,OAAA;MAAKuC,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdC,eAAe,EAAE,SAAS;QAC1BE,OAAO,EAAE,MAAM;QACfI,UAAU,EAAE,QAAQ;QACpBC,WAAW,EAAE,MAAM;QACnBC,YAAY,EAAE;MAChB,CAAE;MAAAH,QAAA,EACC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAACgB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrFhE,OAAA;QAAiBuC,KAAK,EAAE;UACtBoB,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,MAAM;UAChBK,OAAO,EAAE,UAAU;UACnBC,MAAM,EAAE,SAAS;UACjBd,YAAY,EAAE,KAAK;UACnBe,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGjD,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACmB,KAAK,CAACE,eAAe,GAAG,SAAU;QAChE4B,YAAY,EAAGlD,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACmB,KAAK,CAACE,eAAe,GAAG,aAAc;QAAAK,QAAA,EAEjEiB;MAAI,GAXGC,KAAK;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNxD,OAAA;MAAKuC,KAAK,EAAE;QACVkB,IAAI,EAAE,CAAC;QACPd,OAAO,EAAE,MAAM;QACfE,QAAQ,EAAE;MACZ,CAAE;MAAAC,QAAA,gBAEA9C,OAAA;QAAKuC,KAAK,EAAE;UACVkB,IAAI,EAAE,CAAC;UACPhB,eAAe,EAAE,SAAS;UAC1BE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvB0B,QAAQ,EAAE;QACZ,CAAE;QAAAxB,QAAA,gBAEA9C,OAAA;UAAKuC,KAAK,EAAE;YACVkB,IAAI,EAAE,CAAC;YACPd,OAAO,EAAE,MAAM;YACfI,UAAU,EAAE,QAAQ;YACpBwB,cAAc,EAAE,QAAQ;YACxBN,OAAO,EAAE,MAAM;YACfK,QAAQ,EAAE;UACZ,CAAE;UAAAxB,QAAA,GACCvC,KAAK,iBACJP,OAAA;YAAKuC,KAAK,EAAE;cACV+B,QAAQ,EAAE,UAAU;cACpBE,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,MAAM;cACZC,KAAK,EAAE,MAAM;cACbjC,eAAe,EAAE,SAAS;cAC1BkB,KAAK,EAAE,OAAO;cACdM,OAAO,EAAE,MAAM;cACfb,YAAY,EAAE,KAAK;cACnBQ,QAAQ,EAAE,MAAM;cAChBe,MAAM,EAAE;YACV,CAAE;YAAA7B,QAAA,gBACA9C,OAAA;cAAA8C,QAAA,EAAQ;YAAG;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjD,KAAK,eAC3BP,OAAA;cACE4E,OAAO,EAAEzC,WAAY;cACrBI,KAAK,EAAE;gBACLsC,UAAU,EAAE,MAAM;gBAClBZ,OAAO,EAAE,SAAS;gBAClBxB,eAAe,EAAE,uBAAuB;gBACxCkB,KAAK,EAAE,OAAO;gBACdmB,MAAM,EAAE,MAAM;gBACd1B,YAAY,EAAE,KAAK;gBACnBc,MAAM,EAAE,SAAS;gBACjBN,QAAQ,EAAE;cACZ,CAAE;cAAAd,QAAA,EACH;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEAnD,SAAS,iBACRL,OAAA;YAAKuC,KAAK,EAAE;cACVY,KAAK,EAAE,MAAM;cACbX,MAAM,EAAE,MAAM;cACdG,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBG,UAAU,EAAE,QAAQ;cACpBwB,cAAc,EAAE,QAAQ;cACxB9B,eAAe,EAAE,SAAS;cAC1BkB,KAAK,EAAE,MAAM;cACbW,QAAQ,EAAE;YACZ,CAAE;YAAAxB,QAAA,gBACA9C,OAAA;cAAKuC,KAAK,EAAE;gBAAEwC,YAAY,EAAE,MAAM;gBAAEnB,QAAQ,EAAE;cAAO,CAAE;cAAAd,QAAA,EAAC;YAExD;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNxD,OAAA;cAAKuC,KAAK,EAAE;gBACVY,KAAK,EAAE,MAAM;gBACbX,MAAM,EAAE,KAAK;gBACbC,eAAe,EAAE,MAAM;gBACvBW,YAAY,EAAE,KAAK;gBACnBP,QAAQ,EAAE,QAAQ;gBAClBmC,MAAM,EAAE;cACV,CAAE;cAAAlC,QAAA,eACA9C,OAAA;gBAAKuC,KAAK,EAAE;kBACVY,KAAK,EAAE,MAAM;kBACbX,MAAM,EAAE,MAAM;kBACdC,eAAe,EAAE,SAAS;kBAC1BwC,SAAS,EAAE;gBACb;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEA,CAACrD,MAAM,IAAI,CAACE,SAAS,iBACpBL,OAAA;YAAKuC,KAAK,EAAE;cACVmB,SAAS,EAAE,QAAQ;cACnBC,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE;YACZ,CAAE;YAAAd,QAAA,gBACA9C,OAAA;cAAKuC,KAAK,EAAE;gBAAEqB,QAAQ,EAAE,MAAM;gBAAEmB,YAAY,EAAE;cAAO,CAAE;cAAAjC,QAAA,EAAC;YAAE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChExD,OAAA;cAAA8C,QAAA,EAAK;YAAkB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CACN,EAEArD,MAAM,iBAAIH,OAAA,CAACF,UAAU;YAACK,MAAM,EAAEA,MAAO;YAACM,aAAa,EAAEA;UAAc;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAGNxD,OAAA;UAAKuC,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,eAAe,EAAE,MAAM;YACvByC,SAAS,EAAE,gBAAgB;YAC3BvC,OAAO,EAAE,MAAM;YACfI,UAAU,EAAE,QAAQ;YACpBC,WAAW,EAAE,MAAM;YACnBY,QAAQ,EAAE,MAAM;YAChBD,KAAK,EAAE;UACT,CAAE;UAAAb,QAAA,EAAC;QAEH;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxD,OAAA;QAAKuC,KAAK,EAAE;UACVY,KAAK,EAAE,OAAO;UACdV,eAAe,EAAE,SAAS;UAC1B0C,UAAU,EAAE,gBAAgB;UAC5BxC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE;QACjB,CAAE;QAAAE,QAAA,gBAEA9C,OAAA;UAAKuC,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,eAAe,EAAE,SAAS;YAC1BQ,YAAY,EAAE,gBAAgB;YAC9BN,OAAO,EAAE,MAAM;YACfI,UAAU,EAAE,QAAQ;YACpBwB,cAAc,EAAE,QAAQ;YACxBZ,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAf,QAAA,EAAC;QAEH;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGNxD,OAAA;UAAKuC,KAAK,EAAE;YACVkB,IAAI,EAAE,CAAC;YACPZ,QAAQ,EAAE,MAAM;YAChBoB,OAAO,EAAE;UACX,CAAE;UAAAnB,QAAA,eACA9C,OAAA,CAACH,UAAU;YACTuF,QAAQ,EAAExE,YAAa;YACvBP,SAAS,EAAEA,SAAU;YACrBgF,GAAG,EAAE1E;UAAc;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNxD,OAAA;UAAKuC,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,eAAe,EAAE,MAAM;YACvByC,SAAS,EAAE,gBAAgB;YAC3BvC,OAAO,EAAE,MAAM;YACfI,UAAU,EAAE,QAAQ;YACpBwB,cAAc,EAAE,QAAQ;YACxBrB,GAAG,EAAE,MAAM;YACXe,OAAO,EAAE;UACX,CAAE;UAAAnB,QAAA,gBACA9C,OAAA;YACE4E,OAAO,EAAEzC,WAAY;YACrBI,KAAK,EAAE;cACLkB,IAAI,EAAE,CAAC;cACPQ,OAAO,EAAE,KAAK;cACdxB,eAAe,EAAE,MAAM;cACvBkB,KAAK,EAAE,OAAO;cACdmB,MAAM,EAAE,MAAM;cACd1B,YAAY,EAAE,KAAK;cACnBc,MAAM,EAAE,SAAS;cACjBN,QAAQ,EAAE;YACZ,CAAE;YAAAd,QAAA,EACH;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxD,OAAA;YACE4E,OAAO,EAAExC,WAAY;YACrBkD,QAAQ,EAAEjF,SAAU;YACpBkC,KAAK,EAAE;cACLkB,IAAI,EAAE,CAAC;cACPQ,OAAO,EAAE,KAAK;cACdxB,eAAe,EAAEpC,SAAS,GAAG,MAAM,GAAG,SAAS;cAC/CsD,KAAK,EAAE,OAAO;cACdmB,MAAM,EAAE,MAAM;cACd1B,YAAY,EAAE,KAAK;cACnBc,MAAM,EAAE7D,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CuD,QAAQ,EAAE;YACZ,CAAE;YAAAd,QAAA,EAEDzC,SAAS,GAAG,QAAQ,GAAG;UAAI;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxD,OAAA;MAAOuF,GAAG;MAAAzC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACtD,EAAA,CA/TQD,GAAG;AAAAuF,EAAA,GAAHvF,GAAG;AAiUZ,eAAeA,GAAG;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}