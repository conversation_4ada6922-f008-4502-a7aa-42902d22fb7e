{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResultView = ({\n  result,\n  originalImage\n}) => {\n  _s();\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const containerRef = useRef(null);\n  const enhancedImageRef = useRef(null);\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n    setImageError(false);\n  };\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleMouseDown = e => {\n    setIsDragging(true);\n    e.preventDefault();\n  };\n  const handleMouseMove = e => {\n    if (!isDragging || !containerRef.current) return;\n    const rect = containerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, x / rect.width * 100));\n    setSplitPosition(percentage);\n  };\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#ddd'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '40px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        padding: '0 16px',\n        flexShrink: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '13px',\n            color: '#ddd'\n          },\n          children: \"\\u5904\\u7406\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), result.message && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: [\"\\u2022 \", result.message]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '6px',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('split'),\n          style: {\n            padding: '4px 8px',\n            backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u5206\\u5272\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('side-by-side'),\n          style: {\n            padding: '4px 8px',\n            backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u5E76\\u6392\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), viewMode === 'split' && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: resetSplit,\n          style: {\n            padding: '4px 8px',\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u91CD\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), result.params_used && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: showParams ? 'auto' : '32px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        padding: '8px 16px',\n        flexShrink: 0,\n        transition: 'height 0.3s ease'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: \"\\u5904\\u7406\\u53C2\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this), !showParams && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '12px',\n              fontSize: '12px',\n              color: '#ddd'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [result.params_used.scale, \"x\\u8D85\\u5206\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: result.params_used.use_realesrgan ? 'RealESRGAN' : '插值'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u9510\\u5316\", (result.params_used.sharpening * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u7F8E\\u989C\", (result.params_used.beauty * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowParams(!showParams),\n          style: {\n            padding: '2px 6px',\n            backgroundColor: 'transparent',\n            color: '#aaa',\n            border: '1px solid #555',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '11px',\n            transition: 'all 0.2s'\n          },\n          onMouseEnter: e => {\n            e.target.style.backgroundColor = '#555';\n            e.target.style.color = '#fff';\n          },\n          onMouseLeave: e => {\n            e.target.style.backgroundColor = 'transparent';\n            e.target.style.color = '#aaa';\n          },\n          children: showParams ? '▲' : '▼'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this), showParams && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '12px',\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',\n          gap: '8px',\n          fontSize: '12px',\n          color: '#ddd'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u8D85\\u5206\\u500D\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 20\n          }, this), \" \", result.params_used.scale, \"x\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"AI\\u6A21\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 20\n          }, this), \" \", result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u9510\\u5316:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 20\n          }, this), \" \", (result.params_used.sharpening * 100).toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u964D\\u566A:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 20\n          }, this), \" \", result.params_used.denoising]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u9971\\u548C\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 20\n          }, this), \" \", result.params_used.saturation.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u5BF9\\u6BD4\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 20\n          }, this), \" \", result.params_used.contrast.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u4EAE\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 20\n          }, this), \" \", result.params_used.brightness > 0 ? '+' : '', result.params_used.brightness]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u7F8E\\u989C:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 20\n          }, this), \" \", (result.params_used.beauty * 100).toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column',\n        overflow: 'hidden',\n        position: 'relative'\n      },\n      children: viewMode === 'split' ?\n      /*#__PURE__*/\n      // 分割线对比模式\n      _jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          padding: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '24px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            marginBottom: '12px',\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"\\u5DE6\\u4FA7\\uFF1A\\u539F\\u59CB\\u56FE\\u50CF | \\u53F3\\u4FA7\\uFF1A\\u589E\\u5F3A\\u56FE\\u50CF | \\u5206\\u5272\\u4F4D\\u7F6E\\uFF1A\", splitPosition.toFixed(0), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: containerRef,\n          style: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            cursor: isDragging ? 'ew-resize' : 'default',\n            userSelect: 'none',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative',\n              width: '100%',\n              height: '100%',\n              maxHeight: 'calc(100vh - 200px)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative',\n                display: 'inline-block',\n                border: '1px solid #555',\n                borderRadius: '4px',\n                overflow: 'hidden'\n              },\n              children: [imageError ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '400px',\n                  height: '300px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: '#ff6b6b',\n                  backgroundColor: '#2b2b2b'\n                },\n                children: \"\\u589E\\u5F3A\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n                ref: enhancedImageRef,\n                src: `http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,\n                alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n                onLoad: handleImageLoad,\n                onError: handleImageError,\n                style: {\n                  maxWidth: '100%',\n                  maxHeight: 'calc(100vh - 250px)',\n                  height: 'auto',\n                  display: 'block',\n                  opacity: imageLoaded ? 1 : 0.5,\n                  imageRendering: 'auto' // 增强图像使用高质量渲染\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 23\n              }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  width: `${splitPosition}%`,\n                  height: '100%',\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: originalImage,\n                  alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n                  style: {\n                    // 确保原图与增强图片尺寸完全匹配\n                    width: `${100 * 100 / splitPosition}%`,\n                    height: '100%',\n                    objectFit: 'fill',\n                    // 强制填充，实现最近邻插值效果\n                    imageRendering: 'pixelated',\n                    // 最近邻插值，保持像素清晰\n                    position: 'absolute',\n                    left: 0,\n                    top: 0\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 23\n              }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: 0,\n                  left: `${splitPosition}%`,\n                  width: '2px',\n                  height: '100%',\n                  backgroundColor: '#4a90e2',\n                  cursor: 'ew-resize',\n                  boxShadow: '0 0 4px rgba(74, 144, 226, 0.5)',\n                  transform: 'translateX(-1px)',\n                  zIndex: 10\n                },\n                onMouseDown: handleMouseDown,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    width: '16px',\n                    height: '32px',\n                    backgroundColor: '#4a90e2',\n                    borderRadius: '8px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '10px',\n                    fontWeight: 'bold',\n                    boxShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                  },\n                  children: \"\\u27F7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 23\n              }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '50%',\n                  left: '50%',\n                  transform: 'translate(-50%, -50%)',\n                  color: '#aaa',\n                  backgroundColor: 'rgba(43,43,43,0.9)',\n                  padding: '8px 12px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                },\n                children: \"\\u52A0\\u8F7D\\u4E2D...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      // 并排对比模式\n      _jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          padding: '16px',\n          gap: '16px',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: [originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: '1',\n            textAlign: 'center',\n            maxWidth: '50%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '8px',\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: \"\\u539F\\u59CB\\u56FE\\u50CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: originalImage,\n            alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n            style: {\n              maxWidth: '100%',\n              maxHeight: 'calc(100vh - 250px)',\n              height: 'auto',\n              border: '1px solid #4a90e2',\n              borderRadius: '4px',\n              objectFit: 'contain'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: '1',\n            textAlign: 'center',\n            maxWidth: '50%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '8px',\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: \"\\u589E\\u5F3A\\u56FE\\u50CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 15\n          }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              height: '200px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              border: '1px dashed #666',\n              borderRadius: '4px',\n              color: '#aaa',\n              backgroundColor: '#2b2b2b'\n            },\n            children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u56FE\\u50CF...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 17\n          }, this), imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              height: '200px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              border: '1px solid #ff6b6b',\n              borderRadius: '4px',\n              color: '#ff6b6b',\n              backgroundColor: '#2b2b2b'\n            },\n            children: \"\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,\n            alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n            onLoad: handleImageLoad,\n            onError: handleImageError,\n            style: {\n              maxWidth: '100%',\n              maxHeight: 'calc(100vh - 250px)',\n              height: 'auto',\n              border: '1px solid #28ca42',\n              borderRadius: '4px',\n              objectFit: 'contain',\n              display: imageLoaded ? 'block' : 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '48px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        gap: '12px',\n        padding: '0 16px',\n        flexShrink: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: downloadImage,\n        style: {\n          padding: '6px 12px',\n          backgroundColor: '#28ca42',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer',\n          fontSize: '13px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '6px',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.target.style.backgroundColor = '#22a83a',\n        onMouseLeave: e => e.target.style.backgroundColor = '#28ca42',\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCE5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 13\n        }, this), \"\\u4E0B\\u8F7D\\u56FE\\u50CF\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: `http://localhost:8001/result/${result.enhanced_url}`,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        style: {\n          padding: '6px 12px',\n          backgroundColor: '#4a90e2',\n          color: 'white',\n          textDecoration: 'none',\n          borderRadius: '4px',\n          fontSize: '13px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '6px',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.target.style.backgroundColor = '#357abd',\n        onMouseLeave: e => e.target.style.backgroundColor = '#4a90e2',\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDD0D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 13\n        }, this), \"\\u65B0\\u7A97\\u53E3\\u67E5\\u770B\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#aaa',\n          marginLeft: 'auto'\n        },\n        children: [\"\\u6587\\u4EF6: \", result.filename]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultView, \"BJnMeBTVeyJvRi8o2uxRu2RDMSE=\");\n_c = ResultView;\nexport default ResultView;\nvar _c;\n$RefreshReg$(_c, \"ResultView\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "ResultView", "result", "originalImage", "_s", "imageLoaded", "setImageLoaded", "imageError", "setImageError", "showParams", "setShowParams", "splitPosition", "setSplitPosition", "isDragging", "setIsDragging", "viewMode", "setViewMode", "containerRef", "enhancedImageRef", "handleImageLoad", "handleImageError", "downloadImage", "link", "document", "createElement", "href", "enhanced_url", "download", "filename", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleMouseDown", "e", "preventDefault", "handleMouseMove", "current", "rect", "getBoundingClientRect", "x", "clientX", "left", "percentage", "Math", "max", "min", "width", "handleMouseUp", "addEventListener", "removeEventListener", "resetSplit", "style", "height", "display", "flexDirection", "backgroundColor", "color", "children", "borderBottom", "alignItems", "justifyContent", "padding", "flexShrink", "gap", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "message", "onClick", "border", "cursor", "transition", "params_used", "scale", "use_realesrgan", "sharpening", "toFixed", "beauty", "onMouseEnter", "target", "onMouseLeave", "marginTop", "gridTemplateColumns", "denoising", "saturation", "contrast", "brightness", "flex", "overflow", "position", "marginBottom", "ref", "userSelect", "maxHeight", "src", "Date", "now", "alt", "onLoad", "onError", "max<PERSON><PERSON><PERSON>", "opacity", "imageRendering", "top", "objectFit", "boxShadow", "transform", "zIndex", "onMouseDown", "fontWeight", "textAlign", "borderTop", "rel", "textDecoration", "marginLeft", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\n\nconst ResultView = ({ result, originalImage }) => {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const containerRef = useRef(null);\n  const enhancedImageRef = useRef(null);\n\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n    setImageError(false);\n  };\n\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleMouseDown = (e) => {\n    setIsDragging(true);\n    e.preventDefault();\n  };\n\n  const handleMouseMove = (e) => {\n    if (!isDragging || !containerRef.current) return;\n\n    const rect = containerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));\n    setSplitPosition(percentage);\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n\n  return (\n    <div style={{ \n      width: '100%', \n      height: '100%', \n      display: 'flex', \n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#ddd'\n    }}>\n      {/* 顶部工具栏 */}\n      <div style={{\n        height: '40px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        padding: '0 16px',\n        flexShrink: 0\n      }}>\n        {/* 左侧状态指示 */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div style={{\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }}></div>\n          <span style={{ fontSize: '13px', color: '#ddd' }}>处理完成</span>\n          {result.message && (\n            <span style={{ fontSize: '12px', color: '#aaa' }}>• {result.message}</span>\n          )}\n        </div>\n\n        {/* 右侧视图控制 */}\n        <div style={{ display: 'flex', gap: '6px', alignItems: 'center' }}>\n          <button\n            onClick={() => setViewMode('split')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '12px',\n              transition: 'background-color 0.2s'\n            }}\n          >\n            分割对比\n          </button>\n          <button\n            onClick={() => setViewMode('side-by-side')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '12px',\n              transition: 'background-color 0.2s'\n            }}\n          >\n            并排对比\n          </button>\n          {viewMode === 'split' && (\n            <button\n              onClick={resetSplit}\n              style={{\n                padding: '4px 8px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '12px',\n                transition: 'background-color 0.2s'\n              }}\n            >\n              重置\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* 参数信息栏 */}\n      {result.params_used && (\n        <div style={{\n          height: showParams ? 'auto' : '32px',\n          backgroundColor: '#2b2b2b',\n          borderBottom: '1px solid #555',\n          padding: '8px 16px',\n          flexShrink: 0,\n          transition: 'height 0.3s ease'\n        }}>\n          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n              <span style={{ fontSize: '12px', color: '#aaa' }}>处理参数:</span>\n              {!showParams && (\n                <div style={{ display: 'flex', gap: '12px', fontSize: '12px', color: '#ddd' }}>\n                  <span>{result.params_used.scale}x超分</span>\n                  <span>{result.params_used.use_realesrgan ? 'RealESRGAN' : '插值'}</span>\n                  <span>锐化{(result.params_used.sharpening * 100).toFixed(0)}%</span>\n                  <span>美颜{(result.params_used.beauty * 100).toFixed(0)}%</span>\n                </div>\n              )}\n            </div>\n            <button\n              onClick={() => setShowParams(!showParams)}\n              style={{\n                padding: '2px 6px',\n                backgroundColor: 'transparent',\n                color: '#aaa',\n                border: '1px solid #555',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px',\n                transition: 'all 0.2s'\n              }}\n              onMouseEnter={(e) => {\n                e.target.style.backgroundColor = '#555';\n                e.target.style.color = '#fff';\n              }}\n              onMouseLeave={(e) => {\n                e.target.style.backgroundColor = 'transparent';\n                e.target.style.color = '#aaa';\n              }}\n            >\n              {showParams ? '▲' : '▼'}\n            </button>\n          </div>\n\n          {showParams && (\n            <div style={{ \n              marginTop: '12px', \n              display: 'grid', \n              gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', \n              gap: '8px',\n              fontSize: '12px',\n              color: '#ddd'\n            }}>\n              <div><span style={{ color: '#aaa' }}>超分倍数:</span> {result.params_used.scale}x</div>\n              <div><span style={{ color: '#aaa' }}>AI模型:</span> {result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值'}</div>\n              <div><span style={{ color: '#aaa' }}>锐化:</span> {(result.params_used.sharpening * 100).toFixed(0)}%</div>\n              <div><span style={{ color: '#aaa' }}>降噪:</span> {result.params_used.denoising}</div>\n              <div><span style={{ color: '#aaa' }}>饱和度:</span> {result.params_used.saturation.toFixed(1)}</div>\n              <div><span style={{ color: '#aaa' }}>对比度:</span> {result.params_used.contrast.toFixed(1)}</div>\n              <div><span style={{ color: '#aaa' }}>亮度:</span> {result.params_used.brightness > 0 ? '+' : ''}{result.params_used.brightness}</div>\n              <div><span style={{ color: '#aaa' }}>美颜:</span> {(result.params_used.beauty * 100).toFixed(0)}%</div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* 主图像显示区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex', \n        flexDirection: 'column',\n        overflow: 'hidden',\n        position: 'relative'\n      }}>\n        {viewMode === 'split' ? (\n          // 分割线对比模式\n          <div style={{ \n            flex: 1, \n            display: 'flex', \n            flexDirection: 'column',\n            padding: '16px'\n          }}>\n            {/* 分割线信息栏 */}\n            <div style={{\n              height: '24px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              marginBottom: '12px',\n              fontSize: '12px',\n              color: '#aaa'\n            }}>\n              <span>左侧：原始图像 | 右侧：增强图像 | 分割位置：{splitPosition.toFixed(0)}%</span>\n            </div>\n\n            <div\n              ref={containerRef}\n              style={{\n                position: 'relative',\n                width: '100%',\n                height: '100%',\n                cursor: isDragging ? 'ew-resize' : 'default',\n                userSelect: 'none',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}\n            >\n              {originalImage && (\n                <div style={{ \n                  position: 'relative', \n                  width: '100%', \n                  height: '100%',\n                  maxHeight: 'calc(100vh - 200px)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>\n                  {/* 图像对比容器 - 确保像素级对齐 */}\n                  <div style={{\n                    position: 'relative',\n                    display: 'inline-block',\n                    border: '1px solid #555',\n                    borderRadius: '4px',\n                    overflow: 'hidden'\n                  }}>\n                    {/* 增强图像作为背景 */}\n                    {imageError ? (\n                      <div style={{\n                        width: '400px',\n                        height: '300px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        color: '#ff6b6b',\n                        backgroundColor: '#2b2b2b'\n                      }}>\n                        增强图像加载失败\n                      </div>\n                    ) : (\n                      <img\n                        ref={enhancedImageRef}\n                        src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                        alt=\"增强图像\"\n                        onLoad={handleImageLoad}\n                        onError={handleImageError}\n                        style={{\n                          maxWidth: '100%',\n                          maxHeight: 'calc(100vh - 250px)',\n                          height: 'auto',\n                          display: 'block',\n                          opacity: imageLoaded ? 1 : 0.5,\n                          imageRendering: 'auto' // 增强图像使用高质量渲染\n                        }}\n                      />\n                    )}\n\n                    {/* 原始图像覆盖层 - 像素级对齐 */}\n                    {imageLoaded && (\n                      <div\n                        style={{\n                          position: 'absolute',\n                          top: 0,\n                          left: 0,\n                          width: `${splitPosition}%`,\n                          height: '100%',\n                          overflow: 'hidden'\n                        }}\n                      >\n                        <img\n                          src={originalImage}\n                          alt=\"原始图像\"\n                          style={{\n                            // 确保原图与增强图片尺寸完全匹配\n                            width: `${100 * 100 / splitPosition}%`,\n                            height: '100%',\n                            objectFit: 'fill', // 强制填充，实现最近邻插值效果\n                            imageRendering: 'pixelated', // 最近邻插值，保持像素清晰\n                            position: 'absolute',\n                            left: 0,\n                            top: 0\n                          }}\n                        />\n                      </div>\n                    )}\n\n                    {/* 分割线 */}\n                    {imageLoaded && (\n                      <div\n                        style={{\n                          position: 'absolute',\n                          top: 0,\n                          left: `${splitPosition}%`,\n                          width: '2px',\n                          height: '100%',\n                          backgroundColor: '#4a90e2',\n                          cursor: 'ew-resize',\n                          boxShadow: '0 0 4px rgba(74, 144, 226, 0.5)',\n                          transform: 'translateX(-1px)',\n                          zIndex: 10\n                        }}\n                        onMouseDown={handleMouseDown}\n                      >\n                        {/* 分割线手柄 */}\n                        <div\n                          style={{\n                            position: 'absolute',\n                            top: '50%',\n                            left: '50%',\n                            transform: 'translate(-50%, -50%)',\n                            width: '16px',\n                            height: '32px',\n                            backgroundColor: '#4a90e2',\n                            borderRadius: '8px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            color: 'white',\n                            fontSize: '10px',\n                            fontWeight: 'bold',\n                            boxShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                          }}\n                        >\n                          ⟷\n                        </div>\n                      </div>\n                    )}\n\n                    {/* 加载状态 */}\n                    {!imageLoaded && !imageError && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '50%',\n                        left: '50%',\n                        transform: 'translate(-50%, -50%)',\n                        color: '#aaa',\n                        backgroundColor: 'rgba(43,43,43,0.9)',\n                        padding: '8px 12px',\n                        borderRadius: '4px',\n                        fontSize: '12px'\n                      }}>\n                        加载中...\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        ) : (\n          // 并排对比模式\n          <div style={{ \n            flex: 1, \n            display: 'flex', \n            padding: '16px',\n            gap: '16px',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            {/* 原始图像 */}\n            {originalImage && (\n              <div style={{ \n                flex: '1', \n                textAlign: 'center',\n                maxWidth: '50%'\n              }}>\n                <div style={{ \n                  marginBottom: '8px', \n                  fontSize: '12px', \n                  color: '#aaa' \n                }}>\n                  原始图像\n                </div>\n                <img\n                  src={originalImage}\n                  alt=\"原始图像\"\n                  style={{\n                    maxWidth: '100%',\n                    maxHeight: 'calc(100vh - 250px)',\n                    height: 'auto',\n                    border: '1px solid #4a90e2',\n                    borderRadius: '4px',\n                    objectFit: 'contain'\n                  }}\n                />\n              </div>\n            )}\n\n            {/* 增强图像 */}\n            <div style={{ \n              flex: '1', \n              textAlign: 'center',\n              maxWidth: '50%'\n            }}>\n              <div style={{ \n                marginBottom: '8px', \n                fontSize: '12px', \n                color: '#aaa' \n              }}>\n                增强图像\n              </div>\n\n              {!imageLoaded && !imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '200px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px dashed #666',\n                  borderRadius: '4px',\n                  color: '#aaa',\n                  backgroundColor: '#2b2b2b'\n                }}>\n                  正在加载图像...\n                </div>\n              )}\n\n              {imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '200px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px solid #ff6b6b',\n                  borderRadius: '4px',\n                  color: '#ff6b6b',\n                  backgroundColor: '#2b2b2b'\n                }}>\n                  图像加载失败\n                </div>\n              )}\n\n              <img\n                src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                alt=\"增强图像\"\n                onLoad={handleImageLoad}\n                onError={handleImageError}\n                style={{\n                  maxWidth: '100%',\n                  maxHeight: 'calc(100vh - 250px)',\n                  height: 'auto',\n                  border: '1px solid #28ca42',\n                  borderRadius: '4px',\n                  objectFit: 'contain',\n                  display: imageLoaded ? 'block' : 'none'\n                }}\n              />\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 底部控制栏 */}\n      {imageLoaded && (\n        <div style={{\n          height: '48px',\n          backgroundColor: '#333',\n          borderTop: '1px solid #555',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '12px',\n          padding: '0 16px',\n          flexShrink: 0\n        }}>\n          <button\n            onClick={downloadImage}\n            style={{\n              padding: '6px 12px',\n              backgroundColor: '#28ca42',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '13px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px',\n              transition: 'background-color 0.2s'\n            }}\n            onMouseEnter={(e) => e.target.style.backgroundColor = '#22a83a'}\n            onMouseLeave={(e) => e.target.style.backgroundColor = '#28ca42'}\n          >\n            <span>📥</span>\n            下载图像\n          </button>\n\n          <a\n            href={`http://localhost:8001/result/${result.enhanced_url}`}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            style={{\n              padding: '6px 12px',\n              backgroundColor: '#4a90e2',\n              color: 'white',\n              textDecoration: 'none',\n              borderRadius: '4px',\n              fontSize: '13px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px',\n              transition: 'background-color 0.2s'\n            }}\n            onMouseEnter={(e) => e.target.style.backgroundColor = '#357abd'}\n            onMouseLeave={(e) => e.target.style.backgroundColor = '#4a90e2'}\n          >\n            <span>🔍</span>\n            新窗口查看\n          </a>\n\n          <div style={{\n            fontSize: '12px',\n            color: '#aaa',\n            marginLeft: 'auto'\n          }}>\n            文件: {result.filename}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ResultView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,UAAU,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAMqB,YAAY,GAAGpB,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMqB,gBAAgB,GAAGrB,MAAM,CAAC,IAAI,CAAC;EAErC,MAAMsB,eAAe,GAAGA,CAAA,KAAM;IAC5Bb,cAAc,CAAC,IAAI,CAAC;IACpBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMY,gBAAgB,GAAGA,CAAA,KAAM;IAC7BZ,aAAa,CAAC,IAAI,CAAC;IACnBF,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMe,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,gCAAgCvB,MAAM,CAACwB,YAAY,EAAE;IACjEJ,IAAI,CAACK,QAAQ,GAAG,YAAYzB,MAAM,CAAC0B,QAAQ,EAAE;IAC7CL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;IAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;IACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMW,eAAe,GAAIC,CAAC,IAAK;IAC7BpB,aAAa,CAAC,IAAI,CAAC;IACnBoB,CAAC,CAACC,cAAc,CAAC,CAAC;EACpB,CAAC;EAED,MAAMC,eAAe,GAAIF,CAAC,IAAK;IAC7B,IAAI,CAACrB,UAAU,IAAI,CAACI,YAAY,CAACoB,OAAO,EAAE;IAE1C,MAAMC,IAAI,GAAGrB,YAAY,CAACoB,OAAO,CAACE,qBAAqB,CAAC,CAAC;IACzD,MAAMC,CAAC,GAAGN,CAAC,CAACO,OAAO,GAAGH,IAAI,CAACI,IAAI;IAC/B,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAGN,CAAC,GAAGF,IAAI,CAACS,KAAK,GAAI,GAAG,CAAC,CAAC;IACrEnC,gBAAgB,CAAC+B,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1BlC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACAhB,SAAS,CAAC,MAAM;IACd,IAAIe,UAAU,EAAE;MACdU,QAAQ,CAAC0B,gBAAgB,CAAC,WAAW,EAAEb,eAAe,CAAC;MACvDb,QAAQ,CAAC0B,gBAAgB,CAAC,SAAS,EAAED,aAAa,CAAC;MACnD,OAAO,MAAM;QACXzB,QAAQ,CAAC2B,mBAAmB,CAAC,WAAW,EAAEd,eAAe,CAAC;QAC1Db,QAAQ,CAAC2B,mBAAmB,CAAC,SAAS,EAAEF,aAAa,CAAC;MACxD,CAAC;IACH;EACF,CAAC,EAAE,CAACnC,UAAU,CAAC,CAAC;;EAIhB;EACA,MAAMsC,UAAU,GAAGA,CAAA,KAAM;IACvBvC,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,oBACEZ,OAAA;IAAKoD,KAAK,EAAE;MACVL,KAAK,EAAE,MAAM;MACbM,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,eAAe,EAAE,SAAS;MAC1BC,KAAK,EAAE;IACT,CAAE;IAAAC,QAAA,gBAEA1D,OAAA;MAAKoD,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdG,eAAe,EAAE,SAAS;QAC1BG,YAAY,EAAE,gBAAgB;QAC9BL,OAAO,EAAE,MAAM;QACfM,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BC,OAAO,EAAE,QAAQ;QACjBC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,gBAEA1D,OAAA;QAAKoD,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEM,UAAU,EAAE,QAAQ;UAAEI,GAAG,EAAE;QAAO,CAAE;QAAAN,QAAA,gBACjE1D,OAAA;UAAKoD,KAAK,EAAE;YACVL,KAAK,EAAE,KAAK;YACZM,MAAM,EAAE,KAAK;YACbY,YAAY,EAAE,KAAK;YACnBT,eAAe,EAAE;UACnB;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACTrE,OAAA;UAAMoD,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAEb,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,EAAC;QAAI;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC5DnE,MAAM,CAACqE,OAAO,iBACbvE,OAAA;UAAMoD,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAEb,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,GAAC,SAAE,EAACxD,MAAM,CAACqE,OAAO;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC3E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNrE,OAAA;QAAKoD,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEU,GAAG,EAAE,KAAK;UAAEJ,UAAU,EAAE;QAAS,CAAE;QAAAF,QAAA,gBAChE1D,OAAA;UACEwE,OAAO,EAAEA,CAAA,KAAMxD,WAAW,CAAC,OAAO,CAAE;UACpCoC,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAEzC,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,MAAM;YAC1D0C,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrE,OAAA;UACEwE,OAAO,EAAEA,CAAA,KAAMxD,WAAW,CAAC,cAAc,CAAE;UAC3CoC,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAEzC,QAAQ,KAAK,cAAc,GAAG,SAAS,GAAG,MAAM;YACjE0C,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRtD,QAAQ,KAAK,OAAO,iBACnBf,OAAA;UACEwE,OAAO,EAAErB,UAAW;UACpBC,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLnE,MAAM,CAAC0E,WAAW,iBACjB5E,OAAA;MAAKoD,KAAK,EAAE;QACVC,MAAM,EAAE5C,UAAU,GAAG,MAAM,GAAG,MAAM;QACpC+C,eAAe,EAAE,SAAS;QAC1BG,YAAY,EAAE,gBAAgB;QAC9BG,OAAO,EAAE,UAAU;QACnBC,UAAU,EAAE,CAAC;QACbY,UAAU,EAAE;MACd,CAAE;MAAAjB,QAAA,gBACA1D,OAAA;QAAKoD,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEM,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAH,QAAA,gBACrF1D,OAAA;UAAKoD,KAAK,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEM,UAAU,EAAE,QAAQ;YAAEI,GAAG,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACjE1D,OAAA;YAAMoD,KAAK,EAAE;cAAEkB,QAAQ,EAAE,MAAM;cAAEb,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC7D,CAAC5D,UAAU,iBACVT,OAAA;YAAKoD,KAAK,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAEU,GAAG,EAAE,MAAM;cAAEM,QAAQ,EAAE,MAAM;cAAEb,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,gBAC5E1D,OAAA;cAAA0D,QAAA,GAAOxD,MAAM,CAAC0E,WAAW,CAACC,KAAK,EAAC,eAAG;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1CrE,OAAA;cAAA0D,QAAA,EAAOxD,MAAM,CAAC0E,WAAW,CAACE,cAAc,GAAG,YAAY,GAAG;YAAI;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtErE,OAAA;cAAA0D,QAAA,GAAM,cAAE,EAAC,CAACxD,MAAM,CAAC0E,WAAW,CAACG,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClErE,OAAA;cAAA0D,QAAA,GAAM,cAAE,EAAC,CAACxD,MAAM,CAAC0E,WAAW,CAACK,MAAM,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNrE,OAAA;UACEwE,OAAO,EAAEA,CAAA,KAAM9D,aAAa,CAAC,CAACD,UAAU,CAAE;UAC1C2C,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAE,aAAa;YAC9BC,KAAK,EAAE,MAAM;YACbgB,MAAM,EAAE,gBAAgB;YACxBR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UACFO,YAAY,EAAGhD,CAAC,IAAK;YACnBA,CAAC,CAACiD,MAAM,CAAC/B,KAAK,CAACI,eAAe,GAAG,MAAM;YACvCtB,CAAC,CAACiD,MAAM,CAAC/B,KAAK,CAACK,KAAK,GAAG,MAAM;UAC/B,CAAE;UACF2B,YAAY,EAAGlD,CAAC,IAAK;YACnBA,CAAC,CAACiD,MAAM,CAAC/B,KAAK,CAACI,eAAe,GAAG,aAAa;YAC9CtB,CAAC,CAACiD,MAAM,CAAC/B,KAAK,CAACK,KAAK,GAAG,MAAM;UAC/B,CAAE;UAAAC,QAAA,EAEDjD,UAAU,GAAG,GAAG,GAAG;QAAG;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL5D,UAAU,iBACTT,OAAA;QAAKoD,KAAK,EAAE;UACViC,SAAS,EAAE,MAAM;UACjB/B,OAAO,EAAE,MAAM;UACfgC,mBAAmB,EAAE,sCAAsC;UAC3DtB,GAAG,EAAE,KAAK;UACVM,QAAQ,EAAE,MAAM;UAChBb,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,gBACA1D,OAAA;UAAA0D,QAAA,gBAAK1D,OAAA;YAAMoD,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACnE,MAAM,CAAC0E,WAAW,CAACC,KAAK,EAAC,GAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnFrE,OAAA;UAAA0D,QAAA,gBAAK1D,OAAA;YAAMoD,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACnE,MAAM,CAAC0E,WAAW,CAACE,cAAc,GAAG,YAAY,GAAG,MAAM;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnHrE,OAAA;UAAA0D,QAAA,gBAAK1D,OAAA;YAAMoD,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC,CAACnE,MAAM,CAAC0E,WAAW,CAACG,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzGrE,OAAA;UAAA0D,QAAA,gBAAK1D,OAAA;YAAMoD,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACnE,MAAM,CAAC0E,WAAW,CAACW,SAAS;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpFrE,OAAA;UAAA0D,QAAA,gBAAK1D,OAAA;YAAMoD,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACnE,MAAM,CAAC0E,WAAW,CAACY,UAAU,CAACR,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGrE,OAAA;UAAA0D,QAAA,gBAAK1D,OAAA;YAAMoD,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACnE,MAAM,CAAC0E,WAAW,CAACa,QAAQ,CAACT,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FrE,OAAA;UAAA0D,QAAA,gBAAK1D,OAAA;YAAMoD,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACnE,MAAM,CAAC0E,WAAW,CAACc,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAExF,MAAM,CAAC0E,WAAW,CAACc,UAAU;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnIrE,OAAA;UAAA0D,QAAA,gBAAK1D,OAAA;YAAMoD,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC,CAACnE,MAAM,CAAC0E,WAAW,CAACK,MAAM,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClG,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDrE,OAAA;MAAKoD,KAAK,EAAE;QACVuC,IAAI,EAAE,CAAC;QACPrC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBqC,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE;MACZ,CAAE;MAAAnC,QAAA,EACC3C,QAAQ,KAAK,OAAO;MAAA;MACnB;MACAf,OAAA;QAAKoD,KAAK,EAAE;UACVuC,IAAI,EAAE,CAAC;UACPrC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBO,OAAO,EAAE;QACX,CAAE;QAAAJ,QAAA,gBAEA1D,OAAA;UAAKoD,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBiC,YAAY,EAAE,MAAM;YACpBxB,QAAQ,EAAE,MAAM;YAChBb,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,eACA1D,OAAA;YAAA0D,QAAA,GAAM,0HAAyB,EAAC/C,aAAa,CAACqE,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eAENrE,OAAA;UACE+F,GAAG,EAAE9E,YAAa;UAClBmC,KAAK,EAAE;YACLyC,QAAQ,EAAE,UAAU;YACpB9C,KAAK,EAAE,MAAM;YACbM,MAAM,EAAE,MAAM;YACdqB,MAAM,EAAE7D,UAAU,GAAG,WAAW,GAAG,SAAS;YAC5CmF,UAAU,EAAE,MAAM;YAClB1C,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAH,QAAA,EAEDvD,aAAa,iBACZH,OAAA;YAAKoD,KAAK,EAAE;cACVyC,QAAQ,EAAE,UAAU;cACpB9C,KAAK,EAAE,MAAM;cACbM,MAAM,EAAE,MAAM;cACd4C,SAAS,EAAE,qBAAqB;cAChC3C,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAH,QAAA,eAEA1D,OAAA;cAAKoD,KAAK,EAAE;gBACVyC,QAAQ,EAAE,UAAU;gBACpBvC,OAAO,EAAE,cAAc;gBACvBmB,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnB2B,QAAQ,EAAE;cACZ,CAAE;cAAAlC,QAAA,GAECnD,UAAU,gBACTP,OAAA;gBAAKoD,KAAK,EAAE;kBACVL,KAAK,EAAE,OAAO;kBACdM,MAAM,EAAE,OAAO;kBACfC,OAAO,EAAE,MAAM;kBACfM,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE,QAAQ;kBACxBJ,KAAK,EAAE,SAAS;kBAChBD,eAAe,EAAE;gBACnB,CAAE;gBAAAE,QAAA,EAAC;cAEH;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gBAENrE,OAAA;gBACE+F,GAAG,EAAE7E,gBAAiB;gBACtBgF,GAAG,EAAE,gCAAgChG,MAAM,CAACwB,YAAY,MAAMyE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;gBAC3EC,GAAG,EAAC,0BAAM;gBACVC,MAAM,EAAEnF,eAAgB;gBACxBoF,OAAO,EAAEnF,gBAAiB;gBAC1BgC,KAAK,EAAE;kBACLoD,QAAQ,EAAE,MAAM;kBAChBP,SAAS,EAAE,qBAAqB;kBAChC5C,MAAM,EAAE,MAAM;kBACdC,OAAO,EAAE,OAAO;kBAChBmD,OAAO,EAAEpG,WAAW,GAAG,CAAC,GAAG,GAAG;kBAC9BqG,cAAc,EAAE,MAAM,CAAC;gBACzB;cAAE;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,EAGAhE,WAAW,iBACVL,OAAA;gBACEoD,KAAK,EAAE;kBACLyC,QAAQ,EAAE,UAAU;kBACpBc,GAAG,EAAE,CAAC;kBACNjE,IAAI,EAAE,CAAC;kBACPK,KAAK,EAAE,GAAGpC,aAAa,GAAG;kBAC1B0C,MAAM,EAAE,MAAM;kBACduC,QAAQ,EAAE;gBACZ,CAAE;gBAAAlC,QAAA,eAEF1D,OAAA;kBACEkG,GAAG,EAAE/F,aAAc;kBACnBkG,GAAG,EAAC,0BAAM;kBACVjD,KAAK,EAAE;oBACL;oBACAL,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,GAAGpC,aAAa,GAAG;oBACtC0C,MAAM,EAAE,MAAM;oBACduD,SAAS,EAAE,MAAM;oBAAE;oBACnBF,cAAc,EAAE,WAAW;oBAAE;oBAC7Bb,QAAQ,EAAE,UAAU;oBACpBnD,IAAI,EAAE,CAAC;oBACPiE,GAAG,EAAE;kBACP;gBAAE;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,EAGAhE,WAAW,iBACVL,OAAA;gBACEoD,KAAK,EAAE;kBACLyC,QAAQ,EAAE,UAAU;kBACpBc,GAAG,EAAE,CAAC;kBACNjE,IAAI,EAAE,GAAG/B,aAAa,GAAG;kBACzBoC,KAAK,EAAE,KAAK;kBACZM,MAAM,EAAE,MAAM;kBACdG,eAAe,EAAE,SAAS;kBAC1BkB,MAAM,EAAE,WAAW;kBACnBmC,SAAS,EAAE,iCAAiC;kBAC5CC,SAAS,EAAE,kBAAkB;kBAC7BC,MAAM,EAAE;gBACV,CAAE;gBACFC,WAAW,EAAE/E,eAAgB;gBAAAyB,QAAA,eAG7B1D,OAAA;kBACEoD,KAAK,EAAE;oBACLyC,QAAQ,EAAE,UAAU;oBACpBc,GAAG,EAAE,KAAK;oBACVjE,IAAI,EAAE,KAAK;oBACXoE,SAAS,EAAE,uBAAuB;oBAClC/D,KAAK,EAAE,MAAM;oBACbM,MAAM,EAAE,MAAM;oBACdG,eAAe,EAAE,SAAS;oBAC1BS,YAAY,EAAE,KAAK;oBACnBX,OAAO,EAAE,MAAM;oBACfM,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,QAAQ;oBACxBJ,KAAK,EAAE,OAAO;oBACda,QAAQ,EAAE,MAAM;oBAChB2C,UAAU,EAAE,MAAM;oBAClBJ,SAAS,EAAE;kBACb,CAAE;kBAAAnD,QAAA,EACH;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGA,CAAChE,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;gBAAKoD,KAAK,EAAE;kBACVyC,QAAQ,EAAE,UAAU;kBACpBc,GAAG,EAAE,KAAK;kBACVjE,IAAI,EAAE,KAAK;kBACXoE,SAAS,EAAE,uBAAuB;kBAClCrD,KAAK,EAAE,MAAM;kBACbD,eAAe,EAAE,oBAAoB;kBACrCM,OAAO,EAAE,UAAU;kBACnBG,YAAY,EAAE,KAAK;kBACnBK,QAAQ,EAAE;gBACZ,CAAE;gBAAAZ,QAAA,EAAC;cAEH;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;MAAA;MAEN;MACArE,OAAA;QAAKoD,KAAK,EAAE;UACVuC,IAAI,EAAE,CAAC;UACPrC,OAAO,EAAE,MAAM;UACfQ,OAAO,EAAE,MAAM;UACfE,GAAG,EAAE,MAAM;UACXJ,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAH,QAAA,GAECvD,aAAa,iBACZH,OAAA;UAAKoD,KAAK,EAAE;YACVuC,IAAI,EAAE,GAAG;YACTuB,SAAS,EAAE,QAAQ;YACnBV,QAAQ,EAAE;UACZ,CAAE;UAAA9C,QAAA,gBACA1D,OAAA;YAAKoD,KAAK,EAAE;cACV0C,YAAY,EAAE,KAAK;cACnBxB,QAAQ,EAAE,MAAM;cAChBb,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNrE,OAAA;YACEkG,GAAG,EAAE/F,aAAc;YACnBkG,GAAG,EAAC,0BAAM;YACVjD,KAAK,EAAE;cACLoD,QAAQ,EAAE,MAAM;cAChBP,SAAS,EAAE,qBAAqB;cAChC5C,MAAM,EAAE,MAAM;cACdoB,MAAM,EAAE,mBAAmB;cAC3BR,YAAY,EAAE,KAAK;cACnB2C,SAAS,EAAE;YACb;UAAE;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGDrE,OAAA;UAAKoD,KAAK,EAAE;YACVuC,IAAI,EAAE,GAAG;YACTuB,SAAS,EAAE,QAAQ;YACnBV,QAAQ,EAAE;UACZ,CAAE;UAAA9C,QAAA,gBACA1D,OAAA;YAAKoD,KAAK,EAAE;cACV0C,YAAY,EAAE,KAAK;cACnBxB,QAAQ,EAAE,MAAM;cAChBb,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAEL,CAAChE,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;YAAKoD,KAAK,EAAE;cACVL,KAAK,EAAE,MAAM;cACbM,MAAM,EAAE,OAAO;cACfC,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBY,MAAM,EAAE,iBAAiB;cACzBR,YAAY,EAAE,KAAK;cACnBR,KAAK,EAAE,MAAM;cACbD,eAAe,EAAE;YACnB,CAAE;YAAAE,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,EAEA9D,UAAU,iBACTP,OAAA;YAAKoD,KAAK,EAAE;cACVL,KAAK,EAAE,MAAM;cACbM,MAAM,EAAE,OAAO;cACfC,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBY,MAAM,EAAE,mBAAmB;cAC3BR,YAAY,EAAE,KAAK;cACnBR,KAAK,EAAE,SAAS;cAChBD,eAAe,EAAE;YACnB,CAAE;YAAAE,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,eAEDrE,OAAA;YACEkG,GAAG,EAAE,gCAAgChG,MAAM,CAACwB,YAAY,MAAMyE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;YAC3EC,GAAG,EAAC,0BAAM;YACVC,MAAM,EAAEnF,eAAgB;YACxBoF,OAAO,EAAEnF,gBAAiB;YAC1BgC,KAAK,EAAE;cACLoD,QAAQ,EAAE,MAAM;cAChBP,SAAS,EAAE,qBAAqB;cAChC5C,MAAM,EAAE,MAAM;cACdoB,MAAM,EAAE,mBAAmB;cAC3BR,YAAY,EAAE,KAAK;cACnB2C,SAAS,EAAE,SAAS;cACpBtD,OAAO,EAAEjD,WAAW,GAAG,OAAO,GAAG;YACnC;UAAE;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLhE,WAAW,iBACVL,OAAA;MAAKoD,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdG,eAAe,EAAE,MAAM;QACvB2D,SAAS,EAAE,gBAAgB;QAC3B7D,OAAO,EAAE,MAAM;QACfM,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBG,GAAG,EAAE,MAAM;QACXF,OAAO,EAAE,QAAQ;QACjBC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,gBACA1D,OAAA;QACEwE,OAAO,EAAEnD,aAAc;QACvB+B,KAAK,EAAE;UACLU,OAAO,EAAE,UAAU;UACnBN,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,OAAO;UACdgB,MAAM,EAAE,MAAM;UACdR,YAAY,EAAE,KAAK;UACnBS,MAAM,EAAE,SAAS;UACjBJ,QAAQ,EAAE,MAAM;UAChBhB,OAAO,EAAE,MAAM;UACfM,UAAU,EAAE,QAAQ;UACpBI,GAAG,EAAE,KAAK;UACVW,UAAU,EAAE;QACd,CAAE;QACFO,YAAY,EAAGhD,CAAC,IAAKA,CAAC,CAACiD,MAAM,CAAC/B,KAAK,CAACI,eAAe,GAAG,SAAU;QAChE4B,YAAY,EAAGlD,CAAC,IAAKA,CAAC,CAACiD,MAAM,CAAC/B,KAAK,CAACI,eAAe,GAAG,SAAU;QAAAE,QAAA,gBAEhE1D,OAAA;UAAA0D,QAAA,EAAM;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,4BAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETrE,OAAA;QACEyB,IAAI,EAAE,gCAAgCvB,MAAM,CAACwB,YAAY,EAAG;QAC5DyD,MAAM,EAAC,QAAQ;QACfiC,GAAG,EAAC,qBAAqB;QACzBhE,KAAK,EAAE;UACLU,OAAO,EAAE,UAAU;UACnBN,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,OAAO;UACd4D,cAAc,EAAE,MAAM;UACtBpD,YAAY,EAAE,KAAK;UACnBK,QAAQ,EAAE,MAAM;UAChBhB,OAAO,EAAE,MAAM;UACfM,UAAU,EAAE,QAAQ;UACpBI,GAAG,EAAE,KAAK;UACVW,UAAU,EAAE;QACd,CAAE;QACFO,YAAY,EAAGhD,CAAC,IAAKA,CAAC,CAACiD,MAAM,CAAC/B,KAAK,CAACI,eAAe,GAAG,SAAU;QAChE4B,YAAY,EAAGlD,CAAC,IAAKA,CAAC,CAACiD,MAAM,CAAC/B,KAAK,CAACI,eAAe,GAAG,SAAU;QAAAE,QAAA,gBAEhE1D,OAAA;UAAA0D,QAAA,EAAM;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,kCAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJrE,OAAA;QAAKoD,KAAK,EAAE;UACVkB,QAAQ,EAAE,MAAM;UAChBb,KAAK,EAAE,MAAM;UACb6D,UAAU,EAAE;QACd,CAAE;QAAA5D,QAAA,GAAC,gBACG,EAACxD,MAAM,CAAC0B,QAAQ;MAAA;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjE,EAAA,CAnkBIH,UAAU;AAAAsH,EAAA,GAAVtH,UAAU;AAqkBhB,eAAeA,UAAU;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}