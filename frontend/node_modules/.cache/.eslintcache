[{"/Users/<USER>/code/imageenhanceproweb/frontend/src/index.js": "1", "/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js": "2", "/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js": "3", "/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js": "4"}, {"size": 232, "mtime": 1752406602411, "results": "5", "hashOfConfig": "6"}, {"size": 10081, "mtime": 1752481307329, "results": "7", "hashOfConfig": "6"}, {"size": 20311, "mtime": 1752484973445, "results": "8", "hashOfConfig": "6"}, {"size": 11839, "mtime": 1752479699609, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "wg8roi", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/code/imageenhanceproweb/frontend/src/index.js", [], [], "/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js", [], [], "/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js", [], [], "/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js", [], []]